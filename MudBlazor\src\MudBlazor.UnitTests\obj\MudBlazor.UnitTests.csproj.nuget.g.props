﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages;C:\Program Files (x86)\Microsoft\Xamarin\NuGet\</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft\Xamarin\NuGet\" />
  </ItemGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.Bcl.AsyncInterfaces.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.Bcl.AsyncInterfaces.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\Microsoft.Bcl.AsyncInterfaces.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\Microsoft.Bcl.AsyncInterfaces.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.Build.Locator.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.Build.Locator.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\Microsoft.Build.Locator.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\Microsoft.Build.Locator.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.IO.Redist.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Microsoft.IO.Redist.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\Microsoft.IO.Redist.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\Microsoft.IO.Redist.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Newtonsoft.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\Newtonsoft.Json.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\Newtonsoft.Json.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\Newtonsoft.Json.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Buffers.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Buffers.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Buffers.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Buffers.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Collections.Immutable.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Collections.Immutable.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Collections.Immutable.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Collections.Immutable.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.CommandLine.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.CommandLine.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.CommandLine.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.CommandLine.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Memory.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Memory.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Memory.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Memory.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Numerics.Vectors.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Numerics.Vectors.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Numerics.Vectors.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Numerics.Vectors.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Runtime.CompilerServices.Unsafe.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Runtime.CompilerServices.Unsafe.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Runtime.CompilerServices.Unsafe.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Runtime.CompilerServices.Unsafe.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Text.Encodings.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Text.Encodings.Web.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Text.Encodings.Web.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Text.Encodings.Web.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Text.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Text.Json.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Text.Json.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Text.Json.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Threading.Tasks.Extensions.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.Threading.Tasks.Extensions.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.Threading.Tasks.Extensions.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.Threading.Tasks.Extensions.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.ValueTuple.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\System.ValueTuple.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\System.ValueTuple.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\System.ValueTuple.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\cs\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\cs\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\cs\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\cs\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\cs\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\de\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\de\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\de\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\de\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\de\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\es\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\es\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\es\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\es\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\es\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\fr\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\fr\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\fr\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\fr\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\fr\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\it\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\it\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\it\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\it\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\it\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\ja\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\ja\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\ja\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\ja\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\ja\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\ko\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\ko\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\ko\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\ko\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\ko\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\pl\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\pl\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\pl\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\pl\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\pl\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\pt-BR\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\pt-BR\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\pt-BR\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\pt-BR\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\pt-BR\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\ru\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\ru\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\ru\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\ru\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\ru\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\tr\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\tr\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\tr\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\tr\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\tr\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\zh-Hans\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\zh-Hans\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\zh-Hans\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\zh-Hans\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\zh-Hans\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\zh-Hant\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-net472\zh-Hant\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-net472\zh-Hant\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-net472\zh-Hant\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-net472\zh-Hant\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.Build.Locator.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.Build.Locator.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\Microsoft.Build.Locator.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\Microsoft.Build.Locator.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Newtonsoft.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\Newtonsoft.Json.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\Newtonsoft.Json.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\Newtonsoft.Json.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.Collections.Immutable.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.Collections.Immutable.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\System.Collections.Immutable.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\System.Collections.Immutable.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.CommandLine.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.CommandLine.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\System.CommandLine.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\System.CommandLine.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.Text.Encodings.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.Text.Encodings.Web.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\System.Text.Encodings.Web.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\System.Text.Encodings.Web.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.Text.Json.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\System.Text.Json.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\System.Text.Json.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\System.Text.Json.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\cs\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\cs\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\cs\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\cs\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\cs\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\de\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\de\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\de\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\de\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\de\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\es\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\es\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\es\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\es\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\es\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\fr\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\fr\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\fr\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\fr\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\fr\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\it\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\it\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\it\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\it\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\it\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\ja\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\ja\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\ja\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\ja\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\ja\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\ko\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\ko\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\ko\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\ko\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\ko\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\pl\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\pl\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\pl\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\pl\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\pl\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\pt-BR\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\pt-BR\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\pt-BR\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\pt-BR\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\pt-BR\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\ru\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\ru\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\ru\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\ru\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\ru\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\runtimes\browser\lib\net6.0\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\tr\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\tr\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\tr\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\tr\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\tr\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\zh-Hans\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\zh-Hans\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\zh-Hans\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\zh-Hans\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\zh-Hans\System.CommandLine.resources.dll</Link>
    </Content>
    <Content Include="$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\zh-Hant\System.CommandLine.resources.dll" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.workspaces.msbuild\4.12.0\contentFiles\any\any\BuildHost-netcore\zh-Hant\System.CommandLine.resources.dll')">
      <NuGetPackageId>Microsoft.CodeAnalysis.Workspaces.MSBuild</NuGetPackageId>
      <NuGetPackageVersion>4.12.0</NuGetPackageVersion>
      <NuGetItemType>Content</NuGetItemType>
      <Pack>false</Pack>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>BuildHost-netcore\zh-Hant\System.CommandLine.resources.dll</TargetPath>
      <DestinationSubDirectory>BuildHost-netcore\zh-Hant\</DestinationSubDirectory>
      <Private>True</Private>
      <Link>BuildHost-netcore\zh-Hant\System.CommandLine.resources.dll</Link>
    </Content>
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)reportgenerator\5.4.4\build\netstandard2.0\ReportGenerator.props" Condition="Exists('$(NuGetPackageRoot)reportgenerator\5.4.4\build\netstandard2.0\ReportGenerator.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testing.platform\1.5.3\buildTransitive\net9.0\Microsoft.Testing.Platform.props" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.platform\1.5.3\buildTransitive\net9.0\Microsoft.Testing.Platform.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.5.3\buildTransitive\Microsoft.Testing.Platform.MSBuild.props" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.platform.msbuild\1.5.3\buildTransitive\Microsoft.Testing.Platform.MSBuild.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testing.extensions.telemetry\1.5.3\buildTransitive\net9.0\Microsoft.Testing.Extensions.Telemetry.props" Condition="Exists('$(NuGetPackageRoot)microsoft.testing.extensions.telemetry\1.5.3\buildTransitive\net9.0\Microsoft.Testing.Extensions.Telemetry.props')" />
    <Import Project="$(NuGetPackageRoot)nunit3testadapter\5.0.0\build\netcoreapp3.1\NUnit3TestAdapter.props" Condition="Exists('$(NuGetPackageRoot)nunit3testadapter\5.0.0\build\netcoreapp3.1\NUnit3TestAdapter.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly.authentication\9.0.0\buildTransitive\Microsoft.AspNetCore.Components.WebAssembly.Authentication.props" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly.authentication\9.0.0\buildTransitive\Microsoft.AspNetCore.Components.WebAssembly.Authentication.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.11.0\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codeanalysis.analyzers\3.11.0\buildTransitive\Microsoft.CodeAnalysis.Analyzers.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.testplatform.testhost\17.13.0\build\netcoreapp3.1\Microsoft.TestPlatform.TestHost.props" Condition="Exists('$(NuGetPackageRoot)microsoft.testplatform.testhost\17.13.0\build\netcoreapp3.1\Microsoft.TestPlatform.TestHost.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.codecoverage\17.13.0\build\netstandard2.0\Microsoft.CodeCoverage.props" Condition="Exists('$(NuGetPackageRoot)microsoft.codecoverage\17.13.0\build\netstandard2.0\Microsoft.CodeCoverage.props')" />
    <Import Project="$(NuGetPackageRoot)microsoft.net.test.sdk\17.13.0\build\netcoreapp3.1\Microsoft.NET.Test.Sdk.props" Condition="Exists('$(NuGetPackageRoot)microsoft.net.test.sdk\17.13.0\build\netcoreapp3.1\Microsoft.NET.Test.Sdk.props')" />
    <Import Project="$(NuGetPackageRoot)coverlet.msbuild\6.0.4\build\coverlet.msbuild.props" Condition="Exists('$(NuGetPackageRoot)coverlet.msbuild\6.0.4\build\coverlet.msbuild.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgReportGenerator Condition=" '$(PkgReportGenerator)' == '' ">C:\Users\<USER>\.nuget\packages\reportgenerator\5.4.4</PkgReportGenerator>
    <PkgMicrosoft_CodeAnalysis_Analyzers Condition=" '$(PkgMicrosoft_CodeAnalysis_Analyzers)' == '' ">C:\Users\<USER>\.nuget\packages\microsoft.codeanalysis.analyzers\3.11.0</PkgMicrosoft_CodeAnalysis_Analyzers>
  </PropertyGroup>
</Project>