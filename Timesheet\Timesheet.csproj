﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>9a1c93b4-13cf-4ae6-9edd-521f52376cee</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.28" />
    <PackageReference Include="EPPlus" Version="7.6.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="9.0.2" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.7" />
    <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
	  <ProjectReference Include="..\MudBlazor\src\MudBlazor\MudBlazor.csproj" />

	  <PackageReference Include="PetaPoco.Compiled" Version="6.0.683" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

</Project>