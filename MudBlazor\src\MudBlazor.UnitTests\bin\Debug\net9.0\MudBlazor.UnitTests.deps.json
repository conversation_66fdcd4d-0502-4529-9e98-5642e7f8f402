{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MudBlazor.UnitTests/1.0.0": {"dependencies": {"AwesomeAssertions": "7.2.1", "Microsoft.AspNetCore.Razor.Language": "6.0.36", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.12.0", "Microsoft.CodeAnalysis.Workspaces.MSBuild": "4.12.0", "Microsoft.Extensions.TimeProvider.Testing": "8.10.0", "Microsoft.NET.Test.Sdk": "17.13.0", "Moq": "4.20.71", "MudBlazor": "1.0.0", "MudBlazor.Analyzers": "1.0.0", "MudBlazor.Analyzers.TestComponents": "1.0.0", "MudBlazor.SourceGenerator": "1.0.0", "MudBlazor.UnitTests.Shared": "1.0.0", "MudBlazor.UnitTests.Viewer": "1.0.0", "NUnit3TestAdapter": "5.0.0", "ReportGenerator": "5.4.4", "coverlet.msbuild": "6.0.4"}, "runtime": {"MudBlazor.UnitTests.dll": {}}}, "AngleSharp/1.2.0": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "AngleSharp.Css/1.0.0-beta.144": {"dependencies": {"AngleSharp": "1.2.0"}, "runtime": {"lib/net8.0/AngleSharp.Css.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AngleSharp.Diffing/1.0.0": {"dependencies": {"AngleSharp": "1.2.0", "AngleSharp.Css": "1.0.0-beta.144"}, "runtime": {"lib/netstandard2.0/AngleSharp.Diffing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AwesomeAssertions/7.2.1": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net6.0/FluentAssertions.dll": {"assemblyVersion": "7.2.1.0", "fileVersion": "7.2.1.0"}}}, "bunit/1.38.5": {"dependencies": {"bunit.core": "1.38.5", "bunit.web": "1.38.5"}}, "bunit.core/1.38.5": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Bunit.Core.dll": {"assemblyVersion": "1.38.5.407", "fileVersion": "1.38.5.407"}}}, "bunit.web/1.38.5": {"dependencies": {"AngleSharp": "1.2.0", "AngleSharp.Css": "1.0.0-beta.144", "AngleSharp.Diffing": "1.0.0", "Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.1", "Microsoft.AspNetCore.Components.WebAssembly.Authentication": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Localization.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "System.Text.Json": "9.0.0", "bunit.core": "1.38.5"}, "runtime": {"lib/net9.0/Bunit.Web.dll": {"assemblyVersion": "1.38.5.407", "fileVersion": "1.38.5.407"}}}, "Castle.Core/5.1.1": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.1.1.0"}}}, "coverlet.msbuild/6.0.4": {}, "FluentValidation/11.11.0": {"runtime": {"lib/net8.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "11.11.0.0"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.ApplicationInsights/2.22.0": {"dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.ApplicationInsights.dll": {"assemblyVersion": "2.22.0.997", "fileVersion": "2.22.0.997"}}}, "Microsoft.AspNetCore.Authorization/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.1", "Microsoft.AspNetCore.Components.Analyzers": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.1": {}, "Microsoft.AspNetCore.Components.Authorization/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.1", "Microsoft.AspNetCore.Components": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Components.Forms/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.Web/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Forms": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1", "Microsoft.JSInterop": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.1", "Microsoft.Extensions.Configuration.Json": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.JSInterop.WebAssembly": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.WebAssembly.Authentication/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Metadata/9.0.1": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "6.0.36.0", "fileVersion": "6.0.3624.51604"}}}, "Microsoft.Build/17.7.2": {"dependencies": {"Microsoft.Build.Framework": "17.7.2", "Microsoft.NET.StringTools": "17.7.2", "System.Collections.Immutable": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Reflection.MetadataLoadContext": "7.0.0", "System.Security.Permissions": "8.0.0", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "8.0.0"}, "runtime": {"lib/net7.0/Microsoft.Build.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.7.2.37605"}}}, "Microsoft.Build.Framework/17.7.2": {"dependencies": {"System.Security.Permissions": "8.0.0"}, "runtime": {"lib/net7.0/Microsoft.Build.Framework.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.7.2.37605"}}}, "Microsoft.Build.Tasks.Core/17.7.2": {"dependencies": {"Microsoft.Build.Framework": "17.7.2", "Microsoft.Build.Utilities.Core": "17.7.2", "Microsoft.NET.StringTools": "17.7.2", "Microsoft.VisualStudio.Setup.Configuration.Interop": "3.2.2146", "System.CodeDom": "7.0.0", "System.Collections.Immutable": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Resources.Extensions": "8.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.Security.Cryptography.Xml": "7.0.1", "System.Security.Permissions": "8.0.0"}, "runtime": {"lib/net7.0/Microsoft.Build.Tasks.Core.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.7.2.37605"}}}, "Microsoft.Build.Utilities.Core/17.7.2": {"dependencies": {"Microsoft.Build.Framework": "17.7.2", "Microsoft.NET.StringTools": "17.7.2", "Microsoft.VisualStudio.Setup.Configuration.Interop": "3.2.2146", "System.Collections.Immutable": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Security.Permissions": "8.0.0"}, "runtime": {"lib/net7.0/Microsoft.Build.Utilities.Core.dll": {"assemblyVersion": "15.1.0.0", "fileVersion": "17.7.2.37605"}}}, "Microsoft.CodeAnalysis.Analyzers/3.11.0": {}, "Microsoft.CodeAnalysis.Common/4.12.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "********", "fileVersion": "4.1200.24.57207"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.12.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "4.12.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "********", "fileVersion": "4.1200.24.57207"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.12.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.CSharp": "4.12.0", "Microsoft.CodeAnalysis.Common": "4.12.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.12.0", "System.Collections.Immutable": "8.0.0", "System.Composition": "8.0.0", "System.IO.Pipelines": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "********", "fileVersion": "4.1200.24.57207"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.12.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "4.12.0", "System.Collections.Immutable": "8.0.0", "System.Composition": "8.0.0", "System.IO.Pipelines": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "********", "fileVersion": "4.1200.24.57207"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.12.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Build": "17.7.2", "Microsoft.Build.Framework": "17.7.2", "Microsoft.Build.Tasks.Core": "17.7.2", "Microsoft.Build.Utilities.Core": "17.7.2", "Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.Common": "4.12.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.12.0", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1", "Newtonsoft.Json": "13.0.3", "System.CodeDom": "7.0.0", "System.Collections.Immutable": "8.0.0", "System.Composition": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.EventLog": "8.0.0", "System.IO.Pipelines": "8.0.0", "System.Reflection.Metadata": "8.0.0", "System.Resources.Extensions": "8.0.0", "System.Security.Cryptography.Pkcs": "7.0.2", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Security.Cryptography.Xml": "7.0.1", "System.Security.Permissions": "8.0.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "9.0.0", "System.Threading.Channels": "7.0.0", "System.Threading.Tasks.Dataflow": "8.0.0", "System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.ExternalAccess.RazorCompiler.dll": {"assemblyVersion": "********", "fileVersion": "4.1200.24.57207"}, "lib/net8.0/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll": {"assemblyVersion": "********", "fileVersion": "4.1200.24.57207"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeCoverage/17.13.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.124.60202"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Physical": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.1", "Microsoft.Extensions.Configuration.Abstractions": "9.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.1", "Microsoft.Extensions.FileSystemGlobbing": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Localization/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Localization.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Logging/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Primitives/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.TimeProvider.Testing/8.10.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.TimeProvider.Testing.dll": {"assemblyVersion": "8.10.0.0", "fileVersion": "8.1000.24.50207"}}}, "Microsoft.JSInterop/9.0.1": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.JSInterop.WebAssembly/9.0.1": {"dependencies": {"Microsoft.JSInterop": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.NET.StringTools/17.7.2": {"runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.7.2.37605"}}}, "Microsoft.NET.Test.Sdk/17.13.0": {"dependencies": {"Microsoft.CodeCoverage": "17.13.0", "Microsoft.TestPlatform.TestHost": "17.13.0"}}, "Microsoft.Testing.Extensions.Telemetry/1.5.3": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.Testing.Platform": "1.5.3"}, "runtime": {"lib/net9.0/Microsoft.Testing.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "**************"}}, "resources": {"lib/net9.0/cs/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Testing.Extensions.Telemetry.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.5.3": {"dependencies": {"Microsoft.Testing.Platform": "1.5.3"}, "runtime": {"lib/net9.0/Microsoft.Testing.Extensions.TrxReport.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "**************"}}}, "Microsoft.Testing.Extensions.VSTestBridge/1.5.3": {"dependencies": {"Microsoft.ApplicationInsights": "2.22.0", "Microsoft.TestPlatform.ObjectModel": "17.13.0", "Microsoft.Testing.Extensions.Telemetry": "1.5.3", "Microsoft.Testing.Extensions.TrxReport.Abstractions": "1.5.3", "Microsoft.Testing.Platform": "1.5.3"}, "runtime": {"lib/net9.0/Microsoft.Testing.Extensions.VSTestBridge.dll": {"assemblyVersion": "*******", "fileVersion": "**************"}}, "resources": {"lib/net9.0/cs/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Testing.Extensions.VSTestBridge.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform/1.5.3": {"runtime": {"lib/net9.0/Microsoft.Testing.Platform.dll": {"assemblyVersion": "*******", "fileVersion": "**************"}}, "resources": {"lib/net9.0/cs/Microsoft.Testing.Platform.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Testing.Platform.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Testing.Platform.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Testing.Platform.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Testing.Platform.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Testing.Platform.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Testing.Platform.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Testing.Platform.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Testing.Platform.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Testing.Platform.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Testing.Platform.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Testing.Platform.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Testing.Platform.MSBuild/1.5.3": {"dependencies": {"Microsoft.Testing.Platform": "1.5.3"}, "runtime": {"lib/net9.0/Microsoft.Testing.Extensions.MSBuild.dll": {"assemblyVersion": "*******", "fileVersion": "**************"}}, "resources": {"lib/net9.0/cs/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Testing.Extensions.MSBuild.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.ObjectModel/17.13.0": {"dependencies": {"System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.13.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.13.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "17.1300.25.10604"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"runtime": {"lib/netstandard2.1/Microsoft.VisualStudio.Setup.Configuration.Interop.dll": {"assemblyVersion": "*******", "fileVersion": "3.2.2146.50370"}}}, "Moq/4.20.71": {"dependencies": {"Castle.Core": "5.1.1"}, "runtime": {"lib/net6.0/Moq.dll": {"assemblyVersion": "4.20.71.0", "fileVersion": "4.20.71.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NUnit/4.2.2": {"runtime": {"lib/net6.0/nunit.framework.dll": {"assemblyVersion": "4.2.2.0", "fileVersion": "4.2.2.0"}, "lib/net6.0/nunit.framework.legacy.dll": {"assemblyVersion": "4.2.2.0", "fileVersion": "4.2.2.0"}}}, "NUnit3TestAdapter/5.0.0": {"dependencies": {"Microsoft.Testing.Extensions.VSTestBridge": "1.5.3", "Microsoft.Testing.Platform.MSBuild": "1.5.3"}}, "ReportGenerator/5.4.4": {}, "System.CodeDom/7.0.0": {"runtime": {"lib/net7.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Collections.Immutable/8.0.0": {}, "System.Composition/8.0.0": {"dependencies": {"System.Composition.AttributedModel": "8.0.0", "System.Composition.Convention": "8.0.0", "System.Composition.Hosting": "8.0.0", "System.Composition.Runtime": "8.0.0", "System.Composition.TypedParts": "8.0.0"}}, "System.Composition.AttributedModel/8.0.0": {"runtime": {"lib/net8.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.Convention/8.0.0": {"dependencies": {"System.Composition.AttributedModel": "8.0.0"}, "runtime": {"lib/net8.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.Hosting/8.0.0": {"dependencies": {"System.Composition.Runtime": "8.0.0"}, "runtime": {"lib/net8.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.Runtime/8.0.0": {"runtime": {"lib/net8.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Composition.TypedParts/8.0.0": {"dependencies": {"System.Composition.AttributedModel": "8.0.0", "System.Composition.Hosting": "8.0.0", "System.Composition.Runtime": "8.0.0"}, "runtime": {"lib/net8.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/net8.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Diagnostics.DiagnosticSource/5.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Formats.Asn1/7.0.0": {}, "System.IO.Pipelines/8.0.0": {}, "System.Net.Http.Json/9.0.1": {}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}, "System.Reflection.MetadataLoadContext/7.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net7.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Resources.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.Pkcs/7.0.2": {"dependencies": {"System.Formats.Asn1": "7.0.0"}, "runtime": {"lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}}, "System.Security.Cryptography.ProtectedData/8.0.0": {"runtime": {"lib/net8.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Security.Cryptography.Xml/7.0.1": {"dependencies": {"System.Security.Cryptography.Pkcs": "7.0.2"}, "runtime": {"lib/net7.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.222.60605"}}}, "System.Security.Permissions/8.0.0": {"dependencies": {"System.Windows.Extensions": "8.0.0"}, "runtime": {"lib/net8.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/9.0.0": {}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks.Dataflow/8.0.0": {}, "System.Windows.Extensions/8.0.0": {"runtime": {"lib/net8.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "MudBlazor/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.Extensions.Localization": "9.0.1"}, "runtime": {"MudBlazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor.Analyzers/1.0.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.11.0", "Microsoft.CodeAnalysis.CSharp": "4.12.0"}, "runtime": {"MudBlazor.Analyzers.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor.Analyzers.TestComponents/1.0.0": {"dependencies": {"MudBlazor": "1.0.0"}, "runtime": {"MudBlazor.Analyzers.TestComponents.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor.Examples.Data/1.0.0": {"runtime": {"MudBlazor.Examples.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor.SourceGenerator/1.0.0": {"runtime": {"MudBlazor.SourceGenerator.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor.UnitTests.Shared/1.0.0": {"dependencies": {"MudBlazor": "1.0.0", "bunit": "1.38.5", "NUnit": "4.2.2"}, "runtime": {"MudBlazor.UnitTests.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor.UnitTests.Viewer/1.0.0": {"dependencies": {"FluentValidation": "11.11.0", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.1", "MudBlazor": "1.0.0", "MudBlazor.Examples.Data": "1.0.0", "System.Net.Http.Json": "9.0.1"}, "runtime": {"MudBlazor.UnitTests.Viewer.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"MudBlazor.UnitTests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AngleSharp/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-uF/PzSCVcb+b2nqVvHZbOqexoJ9R6QLjonugPf0PQl+0h7YKaFZeXyspctbHe5HGlx7/Iuk5BErtk+t63ac/ZA==", "path": "anglesharp/1.2.0", "hashPath": "anglesharp.1.2.0.nupkg.sha512"}, "AngleSharp.Css/1.0.0-beta.144": {"type": "package", "serviceable": true, "sha512": "sha512-WfyZ1zi5o7fNPgTv0O74nmzyxt9w4tjypwpOCSoeoZDOHtgghc/JqyGHRbQh7Y9sZlJiivQhrQNtm4XAy9LHYA==", "path": "anglesharp.css/1.0.0-beta.144", "hashPath": "anglesharp.css.1.0.0-beta.144.nupkg.sha512"}, "AngleSharp.Diffing/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6OeF2VvqyVaxMOP+wE0fjeaP+0ox2Og26tKDmY3Zf/qugRbd86OjmqoF6ZGyQonyP/zPjJ/TAB9VUR4HG3Dq5A==", "path": "anglesharp.diffing/1.0.0", "hashPath": "anglesharp.diffing.1.0.0.nupkg.sha512"}, "AwesomeAssertions/7.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-0H4VcwixfaQ3IFVONK/PYUcLc/l6PidgdIcgMlg4jiurHBiQ/3yzUwunmngMkkb3Hi1A5lkNReued9az88+yzA==", "path": "awesomeassertions/7.2.1", "hashPath": "awesomeassertions.7.2.1.nupkg.sha512"}, "bunit/1.38.5": {"type": "package", "serviceable": true, "sha512": "sha512-QzUM6j3vX1cxGB79SaWOWVhP+fr0vrU/UYjjZdeuEIi59DMwAY35fz+ZPwW+z1AEoBaiMCje3BrBsjuS44y8cQ==", "path": "bunit/1.38.5", "hashPath": "bunit.1.38.5.nupkg.sha512"}, "bunit.core/1.38.5": {"type": "package", "serviceable": true, "sha512": "sha512-1tTNaTk2PIibXW/FohOESnIX7qt9HIYqdhJfzHiTGZcX9DQyoEkgqsstHKq/S3clZJdRU5wHDZA3nfhP7uSkpw==", "path": "bunit.core/1.38.5", "hashPath": "bunit.core.1.38.5.nupkg.sha512"}, "bunit.web/1.38.5": {"type": "package", "serviceable": true, "sha512": "sha512-uruHfxJrP5WTttKDv7V2+T9XaPgZExHLLIzqgl3n/Z45lfbFStfbmpIL+mNBwwItQKhpjHWDWjhlU0Vy9t6jvw==", "path": "bunit.web/1.38.5", "hashPath": "bunit.web.1.38.5.nupkg.sha512"}, "Castle.Core/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-rpYtIczkzGpf+EkZgDr9CClTdemhsrwA/W5hMoPjLkRFnXzH44zDLoovXeKtmxb1ykXK9aJVODSpiJml8CTw2g==", "path": "castle.core/5.1.1", "hashPath": "castle.core.5.1.1.nupkg.sha512"}, "coverlet.msbuild/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-Qa7Hg+wrOMDKpXVn2dw4Wlun490bIWsFW0fdNJQFJLZnbU27MCP0HJ2mPgS+3EQBQUb0zKlkwiQzP+j38Hc3Iw==", "path": "coverlet.msbuild/6.0.4", "hashPath": "coverlet.msbuild.6.0.4.nupkg.sha512"}, "FluentValidation/11.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-cyIVdQBwSipxWG8MA3Rqox7iNbUNUTK5bfJi9tIdm4CAfH71Oo5ABLP4/QyrUwuakqpUEPGtE43BDddvEehuYw==", "path": "fluentvalidation/11.11.0", "hashPath": "fluentvalidation.11.11.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.ApplicationInsights/2.22.0": {"type": "package", "serviceable": true, "sha512": "sha512-3AOM9bZtku7RQwHyMEY3tQMrHIgjcfRDa6YQpd/QG2LDGvMydSlL9Di+8LLMt7J2RDdfJ7/2jdYv6yHcMJAnNw==", "path": "microsoft.applicationinsights/2.22.0", "hashPath": "microsoft.applicationinsights.2.22.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-WgLlLBlMczb2+QLNG6sM95OUZ0EBztz60k/N75tjIgpyu0SdpIfYytAmX/7JJAjRTZF0c/CrWaQV+SH9FuGsrA==", "path": "microsoft.aspnetcore.authorization/9.0.1", "hashPath": "microsoft.aspnetcore.authorization.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6pwfbQKNtvPkbF4tCGiAKGyt6BVpu58xAXz7u2YXcUKTNmNxrymbG1mEyMc0EPzVdnquDDqTyfXM3mC1EJycxQ==", "path": "microsoft.aspnetcore.components/9.0.1", "hashPath": "microsoft.aspnetcore.components.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-I8Rs4LXT5UQxM5Nin2+Oj8aSY2heszSZ3EyTLgt3mxmfiRPrVO7D8NNSsf1voI2Gb0qFJceof/J5c9E+nfNuHw==", "path": "microsoft.aspnetcore.components.analyzers/9.0.1", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Authorization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LD5ApnnUgMAyFDMKXqhyKFksnnxicGxE15dvC6rnOynFzj11Rvf7bENjTP9HUIbD64MYug+wlhl06A4nicw+RQ==", "path": "microsoft.aspnetcore.components.authorization/9.0.0", "hashPath": "microsoft.aspnetcore.components.authorization.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KyULVU32bLz74LWDwPEwNUEllTehzWJuM7YAsz80rMKEzvR0K8cRjRzO0fnN/nfydMeLRRlbI0xj8wnEAymLVw==", "path": "microsoft.aspnetcore.components.forms/9.0.1", "hashPath": "microsoft.aspnetcore.components.forms.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LI0vjYEd9MaDZPDQxPCn4gGYDkEC5U9rp1nWZo7rPozJxgTG2zU3WERujxTi2LeAC2ZzdXlOVCrUyPQ55LZV2A==", "path": "microsoft.aspnetcore.components.web/9.0.1", "hashPath": "microsoft.aspnetcore.components.web.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZZwox99qtrzjQMCdpEd0ZZpotxV0Vabj5+FQkja5IHa8EP6EO/LLHx9mEthdBoi56ltXsXjTpgfEGAGPHN7z+Q==", "path": "microsoft.aspnetcore.components.webassembly/9.0.1", "hashPath": "microsoft.aspnetcore.components.webassembly.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Authentication/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dWGMHCh3/MchcPTvwz8FBd5O1FBE3Dxq6wWayi2xypgADJDTmpQnmMedN90sNVfekXQheAofYh0aPv6+Rt8Zlw==", "path": "microsoft.aspnetcore.components.webassembly.authentication/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.authentication.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EZnHifamF7IFEIyjAKMtJM3I/94OIe72i3P09v5oL0twmsmfQwal6Ni3m8lbB5mge3jWFhMozeW+rUdRSqnXRQ==", "path": "microsoft.aspnetcore.metadata/9.0.1", "hashPath": "microsoft.aspnetcore.metadata.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-n5Mg5D0aRrhHJJ6bJcwKqQydIFcgUq0jTlvuynoJjwA2IvAzh8Aqf9cpYagofQbIlIXILkCP6q6FgbngyVtpYA==", "path": "microsoft.aspnetcore.razor.language/6.0.36", "hashPath": "microsoft.aspnetcore.razor.language.6.0.36.nupkg.sha512"}, "Microsoft.Build/17.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-AmWnumxsMiRycFfE3kq/XnFFTAoPpCWl3UuiKQWCa5Z0+hBKVoiydzS2iXJGd3x+jry+qaTR9GzoezjV9NFT5A==", "path": "microsoft.build/17.7.2", "hashPath": "microsoft.build.17.7.2.nupkg.sha512"}, "Microsoft.Build.Framework/17.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-F+SglYQv6ij5RK4Bmd1X4q01E2ry4M8/huTIZ/1Vk7ZoxdT2J3vmV23cnJZsA/ZLunOTv3B905TU5J1eFmWNPw==", "path": "microsoft.build.framework/17.7.2", "hashPath": "microsoft.build.framework.17.7.2.nupkg.sha512"}, "Microsoft.Build.Tasks.Core/17.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-CmI+mDQ44GWVv0mxFcGYlvek838kK3PgNvXPo/1/Q5/Tc97tajO611uZAj2wNfwJ1kjsCef2Mza4d+SVSyd3Mg==", "path": "microsoft.build.tasks.core/17.7.2", "hashPath": "microsoft.build.tasks.core.17.7.2.nupkg.sha512"}, "Microsoft.Build.Utilities.Core/17.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-soXfaIBW904uEP6WTDv7EbiT0vRfBdNIcqOOEOfyy27WEa8DaXVPQJYSlsDGycS7uTnYU8vlROJbbmlCTBL7hg==", "path": "microsoft.build.utilities.core/17.7.2", "hashPath": "microsoft.build.utilities.core.17.7.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/EW3UE8/lbEYHoC2Qq7AR/DnmvpgdtAMndfQNmpuIMx/Mto8L5JnuCfdBYtgvalQOtfNCnxFejxuRrryvUTsg==", "path": "microsoft.codeanalysis.analyzers/3.11.0", "hashPath": "microsoft.codeanalysis.analyzers.3.11.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-c1kNYihL2gdcuU1dqm8R8YeA4YkB43TpU3pa2r66Uooh6AAhRtENzj9A4Kj0a+H8JDDyuTjNZql9XlVUzV+UjA==", "path": "microsoft.codeanalysis.common/4.12.0", "hashPath": "microsoft.codeanalysis.common.4.12.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-30vVQ1MizeC22iEdEvI2w0eTIYG43/L20yBzuQH01xKzJgHAoWehzI2F8u07o4mXh4DGMOjQF7aEm0zzvsG3Mg==", "path": "microsoft.codeanalysis.csharp/4.12.0", "hashPath": "microsoft.codeanalysis.csharp.4.12.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZZ08UBgL3f3VeVvRA1k+PGZYbCIy2EBX4nBld/1ndsaoUZcwqkUaXbljjpoJ5reozpRrBPMUJ0E34REaVgYKjw==", "path": "microsoft.codeanalysis.csharp.workspaces/4.12.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.12.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-9WFrMPm/k72qo7pxn6hPIS/UIAFVS/2yKBWJAW+kkmcY8PCsuBgp5ms+pmRI3mjAf7J1SmpdgHpRj2x1Gqc+9A==", "path": "microsoft.codeanalysis.workspaces.common/4.12.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.12.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.MSBuild/4.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-IfSNN0IOV++dhLuU8dGBTT4ExLNBhGs7uP7HbbnF/LYAn4I+dpmxDK7kjrfhagT4aDUR7TeRBFWXYg394xN6yA==", "path": "microsoft.codeanalysis.workspaces.msbuild/4.12.0", "hashPath": "microsoft.codeanalysis.workspaces.msbuild.4.12.0.nupkg.sha512"}, "Microsoft.CodeCoverage/17.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-9LIUy0y+DvUmEPtbRDw6Bay3rzwqFV8P4efTrK4CZhQle3M/QwLPjISghfcolmEGAPWxuJi6m98ZEfk4VR4Lfg==", "path": "microsoft.codecoverage/17.13.0", "hashPath": "microsoft.codecoverage.17.13.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-VuthqFS+ju6vT8W4wevdhEFiRi1trvQtkzWLonApfF5USVzzDcTBoY3F24WvN/tffLSrycArVfX1bThm/9xY2A==", "path": "microsoft.extensions.configuration/9.0.1", "hashPath": "microsoft.extensions.configuration.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+4hfFIY1UjBCXFTTOd+ojlDPq6mep3h5Vq5SYE3Pjucr7dNXmq4S/6P/LoVnZFz2e/5gWp/om4svUFgznfULcA==", "path": "microsoft.extensions.configuration.abstractions/9.0.1", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w7kAyu1Mm7eParRV6WvGNNwA8flPTub16fwH49h7b/yqJZFTgYxnOVCuiah3G2bgseJMEq4DLjjsyQRvsdzRgA==", "path": "microsoft.extensions.configuration.binder/9.0.1", "hashPath": "microsoft.extensions.configuration.binder.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-QBOI8YVAyKqeshYOyxSe6co22oag431vxMu5xQe1EjXMkYE4xK4J71xLCW3/bWKmr9Aoy1VqGUARSLFnotk4Bg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-z+g+lgPET1JRDjsOkFe51rkkNcnJgvOK5UIpeTfF1iAi0GkBJz5/yUuTa8a9V8HUh4gj4xFT5WGoMoXoSDKfGg==", "path": "microsoft.extensions.configuration.json/9.0.1", "hashPath": "microsoft.extensions.configuration.json.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-DguZYt1DWL05+8QKWL3b6bW7A2pC5kYFMY5iXM6W2M23jhvcNa8v6AU8PvVJBcysxHwr9/jax0agnwoBumsSwg==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TKDMNRS66UTMEVT38/tU9hA63UTMvzI3DyNm5mx8+JCf3BaOtxgrvWLCI1y3J52PzT5yNl/T2KN5Z0KbApLZcg==", "path": "microsoft.extensions.fileproviders.physical/9.0.1", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Mxcp9NXuQMvAnudRZcgIb5SqlWrlullQzntBLTwuv0MPIJ5LqiGwbRqiyxgdk+vtCoUkplb0oXy5kAw1t469Ug==", "path": "microsoft.extensions.filesystemglobbing/9.0.1", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UgvX4Yb2T3tEsKT30ktZr0H7kTRPapCgEH0bdTwxiEGSdA39/hAQMvvb+vgHpqmevDU5+puyI9ujRkmmbF946w==", "path": "microsoft.extensions.localization/9.0.1", "hashPath": "microsoft.extensions.localization.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CABog43lyaZQMjmlktuImCy6zmAzRBaXqN81uPaMQjlp//ISDVYItZPh6KWpWRF4MY/B67X5oDc3JTUpfdocZw==", "path": "microsoft.extensions.localization.abstractions/9.0.1", "hashPath": "microsoft.extensions.localization.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-E/k5r7S44DOW+08xQPnNbO8DKAQHhkspDboTThNJ6Z3/QBb4LC6gStNWzVmy3IvW7sUD+iJKf4fj0xEkqE7vnQ==", "path": "microsoft.extensions.logging/9.0.1", "hashPath": "microsoft.extensions.logging.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.TimeProvider.Testing/8.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-PqISLycRh3Ym1xBNcmc8OMuqpik0wXkR2uPh8HstyYNXmH06l09hkBaDnRCPo6zBekhaHiGX91Lhbks8nVSZKg==", "path": "microsoft.extensions.timeprovider.testing/8.10.0", "hashPath": "microsoft.extensions.timeprovider.testing.8.10.0.nupkg.sha512"}, "Microsoft.JSInterop/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/xBwIfb0YoC2Muv6EsHjxpqZw2aKv94+i0g0FWZvqvGv3DeAy+8wipAuECVvKYEs2EIclRD41bjajHLoD6mTtw==", "path": "microsoft.jsinterop/9.0.1", "hashPath": "microsoft.jsinterop.9.0.1.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4YMLT96BmWT/BUJ2Btqb34DU8ikpLO3SWHQbe13cIXYmvhgBZGX89T9L/dxCfl7ODBnvyuBpa/E0DgcPHwjdHw==", "path": "microsoft.jsinterop.webassembly/9.0.1", "hashPath": "microsoft.jsinterop.webassembly.9.0.1.nupkg.sha512"}, "Microsoft.NET.StringTools/17.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-GDm2qPXJeWR4FSwY90zYZ+Wd0CN4FE+Nu2F57Vu8avatMzNQxV9WaVEBZFKbT4JLhNcXKc0CKBO50oVoRJR5BQ==", "path": "microsoft.net.stringtools/17.7.2", "hashPath": "microsoft.net.stringtools.17.7.2.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-W19wCPizaIC9Zh47w8wWI/yxuqR7/dtABwOrc8r2jX/8mUNxM2vw4fXDh+DJTeogxV+KzKwg5jNNGQVwf3LXyA==", "path": "microsoft.net.test.sdk/17.13.0", "hashPath": "microsoft.net.test.sdk.17.13.0.nupkg.sha512"}, "Microsoft.Testing.Extensions.Telemetry/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-U9pGd5DQuX1PfkrdFI+xH34JGgQ2nes5QAwIITTk+MQfLvRITqsZjJeHTjpGWh33D/0q1l7aA8/LQHR7UuCgLQ==", "path": "microsoft.testing.extensions.telemetry/1.5.3", "hashPath": "microsoft.testing.extensions.telemetry.1.5.3.nupkg.sha512"}, "Microsoft.Testing.Extensions.TrxReport.Abstractions/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-h34zKNpGyni66VH738mRHeXSnf3klSShUdavUWNhSfWICUUi5aXeI0LBvoX/ad93N0+9xBDU3Fyi6WfxrwKQGw==", "path": "microsoft.testing.extensions.trxreport.abstractions/1.5.3", "hashPath": "microsoft.testing.extensions.trxreport.abstractions.1.5.3.nupkg.sha512"}, "Microsoft.Testing.Extensions.VSTestBridge/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-cJD67YfDT98wEWyazKVD/yPVW6+H1usXeuselCnRes7JZBTIYWtrCchcOzOahnmajT79eDKqt9sta7DXwTDU4Q==", "path": "microsoft.testing.extensions.vstestbridge/1.5.3", "hashPath": "microsoft.testing.extensions.vstestbridge.1.5.3.nupkg.sha512"}, "Microsoft.Testing.Platform/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-WqJydnJ99dEKtquR9HwINz104ehWJKTXbQQrydGatlLRw14bmsx0pa8+E6KUXMYXZAimN0swWlDmcJGjjW4TIg==", "path": "microsoft.testing.platform/1.5.3", "hashPath": "microsoft.testing.platform.1.5.3.nupkg.sha512"}, "Microsoft.Testing.Platform.MSBuild/1.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-bOtpRMSPeT5YLQo+NNY8EtdNTphAUcmALjW4ABU7P0rb6yR2XAZau3TzNieLmR3lRuwudguWzzBhgcLRXwZh0A==", "path": "microsoft.testing.platform.msbuild/1.5.3", "hashPath": "microsoft.testing.platform.msbuild.1.5.3.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-bt0E0Dx+iqW97o4A59RCmUmz/5NarJ7LRL+jXbSHod72ibL5XdNm1Ke+UO5tFhBG4VwHLcSjqq9BUSblGNWamw==", "path": "microsoft.testplatform.objectmodel/17.13.0", "hashPath": "microsoft.testplatform.objectmodel.17.13.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-9GGw08Dc3AXspjekdyTdZ/wYWFlxbgcF0s7BKxzVX+hzAwpifDOdxM+ceVaaJSQOwqt3jtuNlHn3XTpKUS9x9Q==", "path": "microsoft.testplatform.testhost/17.13.0", "hashPath": "microsoft.testplatform.testhost.17.13.0.nupkg.sha512"}, "Microsoft.VisualStudio.Setup.Configuration.Interop/3.2.2146": {"type": "package", "serviceable": true, "sha512": "sha512-gMq8uGy8zTIp0kQGTI45buZC3JOStGJyjGD8gksskk83aQISW65IESErLE/WDT7Bdy+QWbdUi7QyO1LEzUSOFA==", "path": "microsoft.visualstudio.setup.configuration.interop/3.2.2146", "hashPath": "microsoft.visualstudio.setup.configuration.interop.3.2.2146.nupkg.sha512"}, "Moq/4.20.71": {"type": "package", "serviceable": true, "sha512": "sha512-sVyDjL8RDhB2CHYlAc8JCVdULQDqKPLKQj8B9yplEHg+MoNkWcuz3soUpwqTbNmFRJrKgVs8iqFK1SYlBTG5/A==", "path": "moq/4.20.71", "hashPath": "moq.4.20.71.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NUnit/4.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mon0OPko28yZ/foVXrhiUvq1LReaGsBdziumyyYGxV/pOE4q92fuYeN+AF+gEU5pCjzykcdBt5l7xobTaiBjsg==", "path": "nunit/4.2.2", "hashPath": "nunit.4.2.2.nupkg.sha512"}, "NUnit3TestAdapter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sy4cLoUAdE6TDM4wNX5gmNCyhMev5wUz4cA6ZRf/aON9vf9t4xTVGLj/4huhDKcS4dFfmVVcgcP70yC7WC9kKg==", "path": "nunit3testadapter/5.0.0", "hashPath": "nunit3testadapter.5.0.0.nupkg.sha512"}, "ReportGenerator/5.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-DAHP60NtrwdtZndBMJI5si6Pw8W/+kgbb8jjKzSxPwy7Nm1eOgZNAi+xnTEUtsfytrNG9Hex7VpKrf26OR1ypQ==", "path": "reportgenerator/5.4.4", "hashPath": "reportgenerator.5.4.4.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Composition/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E9oO9olNNxA39J8CxQwf7ceIPm+j/B/PhYpyK9M4LhN/OLLRw6u5fNInkhVqaWueMB9iXxYqnwqwgz+W91loIA==", "path": "system.composition/8.0.0", "hashPath": "system.composition.8.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyElSuvmBMYdn2iPG0n29i7Igu0bq99izOP3MAtEwskY3OP9jqsavvVmPn9lesVaj/KT/o/QkNjA43dOJTsDQw==", "path": "system.composition.attributedmodel/8.0.0", "hashPath": "system.composition.attributedmodel.8.0.0.nupkg.sha512"}, "System.Composition.Convention/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UuVkc1B3vQU/LzEbWLMZ1aYVssv4rpShzf8wPEyrUqoGNqdYKREmB8bXR73heOMKkwS6ZnPz3PjGODT2MenukQ==", "path": "system.composition.convention/8.0.0", "hashPath": "system.composition.convention.8.0.0.nupkg.sha512"}, "System.Composition.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qwbONqoxlazxcbiohvb3t1JWZgKIKcRdXS5uEeLbo5wtuBupIbAvdC3PYTAeBCZrZeERvrtAbhYHuuS43Zr1bQ==", "path": "system.composition.hosting/8.0.0", "hashPath": "system.composition.hosting.8.0.0.nupkg.sha512"}, "System.Composition.Runtime/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-G+kRyB5/6+3ucRRQz+DF4uSHGqpkK8Q4ilVdbt4zvxpmvLVZNmSkyFAQpJLcbOyVF85aomJx0m+TGMDVlwx7ZQ==", "path": "system.composition.runtime/8.0.0", "hashPath": "system.composition.runtime.8.0.0.nupkg.sha512"}, "System.Composition.TypedParts/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DsSklhuA+Dsgo3ZZrar8hjBFvq1wa1grrkNCTt+6SoX3vq0Vy+HXJnVXrU/nNH1BjlGH684A7h4hJQHZd/u5mA==", "path": "system.composition.typedparts/8.0.0", "hashPath": "system.composition.typedparts.8.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tCQTzPsGZh/A9LhhA6zrqCRV4hOHsK90/G7q3Khxmn6tnB1PuNU0cRaKANP2AWcF9bn0zsuOoZOSrHuJk6oNBA==", "path": "system.diagnostics.diagnosticsource/5.0.0", "hashPath": "system.diagnostics.diagnosticsource.5.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Formats.Asn1/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+nfpV0afLmvJW8+pLlHxRjz3oZJw4fkyU9MMEaMhCsHi/SN9bGF9q79ROubDiwTiCHezmK0uCWkPP7tGFP/4yg==", "path": "system.formats.asn1/7.0.0", "hashPath": "system.formats.asn1.7.0.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Net.Http.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MhCJ2P2txSz4MMXi7TTutycFdbzPDU/A+pwUNpF6zvbDyZtFtvXJN4EEOuIq8b6sZfarakACnXqr0dTYGjUqzQ==", "path": "system.net.http.json/9.0.1", "hashPath": "system.net.http.json.9.0.1.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-z9PvtMJra5hK8n+g0wmPtaG7HQRZpTmIPRw5Z0LEemlcdQMHuTD5D7OAY/fZuuz1L9db++QOcDF0gJTLpbMtZQ==", "path": "system.reflection.metadataloadcontext/7.0.0", "hashPath": "system.reflection.metadataloadcontext.7.0.0.nupkg.sha512"}, "System.Resources.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-psnQ6GRQOvt+evda5C4nD5EuV49mz2Tv0DD2JDVDEbE/TKoMukxSkGJcsBJ0pajpPuFRr67syFYlkJ4Wj6A5Zw==", "path": "system.resources.extensions/8.0.0", "hashPath": "system.resources.extensions.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xhFNJOcQSWhpiVGLLBQYoxAltQSQVycMkwaX1z7I7oEdT9Wr0HzSM1yeAbfoHaERIYd5s6EpLSOLs2qMchSKlA==", "path": "system.security.cryptography.pkcs/7.0.2", "hashPath": "system.security.cryptography.pkcs.7.0.2.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/7.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MCxBCtH0GrDuvU63ZODwQHQZPchb24pUAX3MfZ6b13qg246ZD10PRdOvay8C9HBPfCXkymUNwFPEegud7ax2zg==", "path": "system.security.cryptography.xml/7.0.1", "hashPath": "system.security.cryptography.xml.7.0.1.nupkg.sha512"}, "System.Security.Permissions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/BBylw7XevuAsHXoX9dDUUfmBIcUf7Lkz8K3ZXIKz3YRKpw8YftpSir4n4e/jDTKFoaK37AsC3xnk+GNFI1Ow==", "path": "system.security.permissions/8.0.0", "hashPath": "system.security.permissions.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Dataflow/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7V0I8tPa9V7UxMx/+7DIwkhls5ouaEMQx6l/GwGm1Y8kJQ61On9B/PxCXFLbgu5/C47g0BP2CUYs+nMv1+Oaqw==", "path": "system.threading.tasks.dataflow/8.0.0", "hashPath": "system.threading.tasks.dataflow.8.0.0.nupkg.sha512"}, "System.Windows.Extensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Obg3a90MkOw9mYKxrardLpY2u0axDMrSmy4JCdq2cYbelM2cUwmUir5Bomvd1yxmPL9h5LVHU1tuKBZpUjfASg==", "path": "system.windows.extensions/8.0.0", "hashPath": "system.windows.extensions.8.0.0.nupkg.sha512"}, "MudBlazor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MudBlazor.Analyzers/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MudBlazor.Analyzers.TestComponents/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MudBlazor.Examples.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MudBlazor.SourceGenerator/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MudBlazor.UnitTests.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MudBlazor.UnitTests.Viewer/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}