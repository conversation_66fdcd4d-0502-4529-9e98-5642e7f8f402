{"Version": 1, "Hash": "UO9zO7rAmRAWOfoYTgp6gB/zuVaBdI1JKrPh0luMqMk=", "Source": "Timesheet", "BasePath": "_content/Timesheet", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\MudBlazor.csproj", "Version": 2, "Source": "MudBlazor", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU;TargetFramework=net9.0", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU;TargetFramework=net9.0", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [{"Name": "MudBlazor\\wwwroot", "Source": "MudBlazor", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "Pattern": "**"}, {"Name": "Timesheet\\wwwroot", "Source": "Timesheet", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint=bj7ppmloxe}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oieqqfqtoo", "Integrity": "AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "FileLength": 11667, "LastWriteTime": "2025-08-15T14:09:40+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint=hmu6kx9mgf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "FileLength": 64916, "LastWriteTime": "2025-08-15T14:09:40+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hmu6kx9mgf", "Integrity": "d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MudBlazor.min.css", "FileLength": 595896, "LastWriteTime": "2025-08-06T21:33:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bj7ppmloxe", "Integrity": "ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MudBlazor.min.js", "FileLength": 55250, "LastWriteTime": "2025-08-06T21:33:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\7fcb08287r-q9pziapwbe.gz", "SourceId": "Timesheet", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Timesheet", "RelativePath": "Timesheet#[.{fingerprint=q9pziapwbe}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Timesheet.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2did7898v6", "Integrity": "NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Timesheet.bundle.scp.css", "FileLength": 367, "LastWriteTime": "2025-08-15T14:13:45+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\l5kpjm1qut-9ihwlfd2u9.gz", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Timesheet", "RelativePath": "app#[.{fingerprint=9ihwlfd2u9}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\app.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7qef4hgepa", "Integrity": "sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\app.css", "FileLength": 2164, "LastWriteTime": "2025-08-15T14:09:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\odye3bcap2-h1lvbfl85b.gz", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Timesheet", "RelativePath": "favicon#[.{fingerprint=h1lvbfl85b}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "y4v6hwmftg", "Integrity": "IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\favicon.ico", "FileLength": 110, "LastWriteTime": "2025-08-15T14:09:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\qt9n398y53-q9pziapwbe.gz", "SourceId": "Timesheet", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Timesheet", "RelativePath": "Timesheet#[.{fingerprint=q9pziapwbe}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Timesheet.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2did7898v6", "Integrity": "NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Timesheet.styles.css", "FileLength": 367, "LastWriteTime": "2025-08-15T14:09:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Timesheet.styles.css", "SourceId": "Timesheet", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\", "BasePath": "_content/Timesheet", "RelativePath": "Timesheet#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "q9pziapwbe", "Integrity": "rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Timesheet.styles.css", "FileLength": 713, "LastWriteTime": "2025-08-15T14:09:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Timesheet.bundle.scp.css", "SourceId": "Timesheet", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\", "BasePath": "_content/Timesheet", "RelativePath": "Timesheet#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "q9pziapwbe", "Integrity": "rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Timesheet.bundle.scp.css", "FileLength": 713, "LastWriteTime": "2025-08-15T14:09:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\app.css", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ihwlfd2u9", "Integrity": "oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 4932, "LastWriteTime": "2025-07-22T18:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\favicon.ico", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h1lvbfl85b", "Integrity": "RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 109, "LastWriteTime": "2025-07-22T18:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\dog.jpg", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "Images/dog#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3fe7agj4s5", "Integrity": "v/4Gjeunwa9vC9CJOX8m3PG+5B3NqyHdkb3uPNniLUs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Images\\dog.jpg", "FileLength": 24863, "LastWriteTime": "2025-07-22T18:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\Polyexpert_logo_0.png", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "Images/Polyexpert_logo_0#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hdmk6ra75i", "Integrity": "u9/gzzPrmU+/94ZELqOL0ovDnimlCNYXdQklh7r/5N0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Images\\Polyexpert_logo_0.png", "FileLength": 6923, "LastWriteTime": "2025-07-22T18:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\simple-black-arrow-on-white.png", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "Images/simple-black-arrow-on-white#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7ui1k0f1nq", "Integrity": "YSYf5tl0IG+760JRhUCtbXgGazShrZlndtJb8xZNHf0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Images\\simple-black-arrow-on-white.png", "FileLength": 1166, "LastWriteTime": "2025-07-22T18:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Timesheets\\2024-2025 Timesheet V4 (version 1).xlsm", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "Timesheets/2024-2025 Timesheet V4 (version 1)#[.{fingerprint}]?.xlsm", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "haxpjows27", "Integrity": "NzjoWFCu+Satx4adxJTy2QeIR8lsEEQL5NWA13B7jQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Timesheets\\2024-2025 Timesheet V4 (version 1).xlsm", "FileLength": 391393, "LastWriteTime": "2025-07-22T18:42:28+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Timesheets\\ExportedTimesheet.xlsx", "SourceId": "Timesheet", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\", "BasePath": "_content/Timesheet", "RelativePath": "Timesheets/ExportedTimesheet#[.{fingerprint}]?.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "59hipdfkal", "Integrity": "J9vqaS5bxuuJBmaukwqvppXscu9u+Vz7ByLqgx7BPXM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Timesheets\\ExportedTimesheet.xlsx", "FileLength": 68640, "LastWriteTime": "2025-07-22T18:42:28+00:00"}], "Endpoints": [{"Route": "_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js.gz"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css.gz"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}]}, {"Route": "app.9ihwlfd2u9.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\l5kpjm1qut-9ihwlfd2u9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461893764"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2164"}, {"Name": "ETag", "Value": "\"sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ihwlfd2u9"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA="}]}, {"Route": "app.9ihwlfd2u9.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4932"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ihwlfd2u9"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA="}]}, {"Route": "app.9ihwlfd2u9.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\l5kpjm1qut-9ihwlfd2u9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2164"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ihwlfd2u9"}, {"Name": "label", "Value": "app.css.gz"}, {"Name": "integrity", "Value": "sha256-sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU="}]}, {"Route": "app.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\l5kpjm1qut-9ihwlfd2u9.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000461893764"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2164"}, {"Name": "ETag", "Value": "\"sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA="}]}, {"Route": "app.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4932"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oUEDFDRQ9BJ97MpRHruvBfib6KSDVMGUBTU7GewHRsA="}]}, {"Route": "app.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\l5kpjm1qut-9ihwlfd2u9.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2164"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sR8gP2UbOGiW7JLkJ1T55AUMMa/Gu6ij4NVnGIxgYVU="}]}, {"Route": "favicon.h1lvbfl85b.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\odye3bcap2-h1lvbfl85b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.009009009009"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110"}, {"Name": "ETag", "Value": "\"IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1lvbfl85b"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4="}]}, {"Route": "favicon.h1lvbfl85b.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1lvbfl85b"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4="}]}, {"Route": "favicon.h1lvbfl85b.ico.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\odye3bcap2-h1lvbfl85b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1lvbfl85b"}, {"Name": "label", "Value": "favicon.ico.gz"}, {"Name": "integrity", "Value": "sha256-IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\odye3bcap2-h1lvbfl85b.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.009009009009"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110"}, {"Name": "ETag", "Value": "\"IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "W/\"RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "109"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RVNfeGspUBY/U5c5FaQxZK0SL9LBWj3dlgdrUVUJ+h4="}]}, {"Route": "favicon.ico.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\odye3bcap2-h1lvbfl85b.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "110"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IjSrMwM2t7EFRxCho6aYqezrnEsTXxQ09VZKItNkdK0="}]}, {"Route": "Images/dog.3fe7agj4s5.jpg", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\dog.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24863"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"v/4Gjeunwa9vC9CJOX8m3PG+5B3NqyHdkb3uPNniLUs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3fe7agj4s5"}, {"Name": "label", "Value": "Images/dog.jpg"}, {"Name": "integrity", "Value": "sha256-v/4Gjeunwa9vC9CJOX8m3PG+5B3NqyHdkb3uPNniLUs="}]}, {"Route": "Images/dog.jpg", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\dog.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24863"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"v/4Gjeunwa9vC9CJOX8m3PG+5B3NqyHdkb3uPNniLUs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v/4Gjeunwa9vC9CJOX8m3PG+5B3NqyHdkb3uPNniLUs="}]}, {"Route": "Images/Polyexpert_logo_0.hdmk6ra75i.png", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\Polyexpert_logo_0.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6923"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"u9/gzzPrmU+/94ZELqOL0ovDnimlCNYXdQklh7r/5N0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hdmk6ra75i"}, {"Name": "label", "Value": "Images/Polyexpert_logo_0.png"}, {"Name": "integrity", "Value": "sha256-u9/gzzPrmU+/94ZELqOL0ovDnimlCNYXdQklh7r/5N0="}]}, {"Route": "Images/Polyexpert_logo_0.png", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\Polyexpert_logo_0.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6923"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"u9/gzzPrmU+/94ZELqOL0ovDnimlCNYXdQklh7r/5N0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9/gzzPrmU+/94ZELqOL0ovDnimlCNYXdQklh7r/5N0="}]}, {"Route": "Images/simple-black-arrow-on-white.7ui1k0f1nq.png", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\simple-black-arrow-on-white.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1166"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YSYf5tl0IG+760JRhUCtbXgGazShrZlndtJb8xZNHf0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7ui1k0f1nq"}, {"Name": "label", "Value": "Images/simple-black-arrow-on-white.png"}, {"Name": "integrity", "Value": "sha256-YSYf5tl0IG+760JRhUCtbXgGazShrZlndtJb8xZNHf0="}]}, {"Route": "Images/simple-black-arrow-on-white.png", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Images\\simple-black-arrow-on-white.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1166"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"YSYf5tl0IG+760JRhUCtbXgGazShrZlndtJb8xZNHf0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YSYf5tl0IG+760JRhUCtbXgGazShrZlndtJb8xZNHf0="}]}, {"Route": "Timesheet.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\7fcb08287r-q9pziapwbe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002717391304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:13:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Timesheet.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.bundle.scp.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\7fcb08287r-q9pziapwbe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:13:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4="}]}, {"Route": "Timesheet.q9pziapwbe.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\7fcb08287r-q9pziapwbe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002717391304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:13:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9pziapwbe"}, {"Name": "label", "Value": "Timesheet.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.q9pziapwbe.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Timesheet.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9pziapwbe"}, {"Name": "label", "Value": "Timesheet.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.q9pziapwbe.bundle.scp.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\7fcb08287r-q9pziapwbe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:13:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9pziapwbe"}, {"Name": "label", "Value": "Timesheet.bundle.scp.css.gz"}, {"Name": "integrity", "Value": "sha256-NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4="}]}, {"Route": "Timesheet.q9pziapwbe.styles.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\qt9n398y53-q9pziapwbe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002717391304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9pziapwbe"}, {"Name": "label", "Value": "Timesheet.styles.css"}, {"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.q9pziapwbe.styles.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Timesheet.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9pziapwbe"}, {"Name": "label", "Value": "Timesheet.styles.css"}, {"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.q9pziapwbe.styles.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\qt9n398y53-q9pziapwbe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q9pziapwbe"}, {"Name": "label", "Value": "Timesheet.styles.css.gz"}, {"Name": "integrity", "Value": "sha256-NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4="}]}, {"Route": "Timesheet.styles.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\qt9n398y53-q9pziapwbe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.002717391304"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.styles.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Timesheet.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "713"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rrSETRAzf8Is5Hsliio7GMHmJQTR4aaNmUQAYjoL+KI="}]}, {"Route": "Timesheet.styles.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\obj\\Debug\\net9.0\\compressed\\qt9n398y53-q9pziapwbe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "367"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NrWSMCf5FF80GfPRuKG3P1zUQK/x5BffVZwu+MMVe/4="}]}, {"Route": "Timesheets/2024-2025 Timesheet V4 (version 1).haxpjows27.xlsm", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Timesheets\\2024-2025 Timesheet V4 (version 1).xlsm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "391393"}, {"Name": "Content-Type", "Value": "application/vnd.ms-excel.sheet.macroEnabled.12"}, {"Name": "ETag", "Value": "\"NzjoWFCu+Satx4adxJTy2QeIR8lsEEQL5NWA13B7jQo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "haxpjows27"}, {"Name": "label", "Value": "Timesheets/2024-2025 Timesheet V4 (version 1).xlsm"}, {"Name": "integrity", "Value": "sha256-NzjoWFCu+Satx4adxJTy2QeIR8lsEEQL5NWA13B7jQo="}]}, {"Route": "Timesheets/2024-2025 Timesheet V4 (version 1).xlsm", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Timesheets\\2024-2025 Timesheet V4 (version 1).xlsm", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "391393"}, {"Name": "Content-Type", "Value": "application/vnd.ms-excel.sheet.macroEnabled.12"}, {"Name": "ETag", "Value": "\"NzjoWFCu+Satx4adxJTy2QeIR8lsEEQL5NWA13B7jQo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NzjoWFCu+Satx4adxJTy2QeIR8lsEEQL5NWA13B7jQo="}]}, {"Route": "Timesheets/ExportedTimesheet.59hipdfkal.xlsx", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Timesheets\\ExportedTimesheet.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68640"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"J9vqaS5bxuuJBmaukwqvppXscu9u+Vz7ByLqgx7BPXM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "59hipdfkal"}, {"Name": "label", "Value": "Timesheets/ExportedTimesheet.xlsx"}, {"Name": "integrity", "Value": "sha256-J9vqaS5bxuuJBmaukwqvppXscu9u+Vz7ByLqgx7BPXM="}]}, {"Route": "Timesheets/ExportedTimesheet.xlsx", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\Timesheet\\wwwroot\\Timesheets\\ExportedTimesheet.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "68640"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"J9vqaS5bxuuJBmaukwqvppXscu9u+Vz7ByLqgx7BPXM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 22 Jul 2025 18:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-J9vqaS5bxuuJBmaukwqvppXscu9u+Vz7ByLqgx7BPXM="}]}]}