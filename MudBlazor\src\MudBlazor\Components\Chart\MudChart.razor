@namespace MudBlazor
@using MudBlazor.Charts
@inherits MudCategoryChartBase

<CascadingValue Value="@this" IsFixed="true">
    <CascadingValue Value="@((MudChartBase)this)" IsFixed="true">
        @ChildContent
        <div @attributes="UserAttributes" class="@Classname" style="@Style" dir="ltr">
            @if (ChartType == ChartType.Donut)
            {
                <Donut InputData="@InputData" @bind-SelectedIndex="@SelectedIndex" InputLabels="@InputLabels"></Donut>
            }
            @if (ChartType == ChartType.Pie)
            {
                <Pie InputData="@InputData" @bind-SelectedIndex="@SelectedIndex" InputLabels="@InputLabels"></Pie>
            }
            @if (ChartType == ChartType.Line)
            {
                <Line ChartSeries="@ChartSeries" @bind-SelectedIndex="@SelectedIndex" XAxisLabels="@XAxisLabels"></Line>
            }
            @if (ChartType == ChartType.Bar)
            {
                <Bar ChartSeries="@ChartSeries" @bind-SelectedIndex="@SelectedIndex" XAxisLabels="@XAxisLabels"></Bar>
            }
            @if (ChartType == ChartType.StackedBar)
            {
                <StackedBar ChartSeries="@ChartSeries" @bind-SelectedIndex="@SelectedIndex" XAxisLabels="@XAxisLabels"></StackedBar>
            }
            @if (ChartType == ChartType.HeatMap)
            {
                <HeatMap ChartSeries="@ChartSeries" @bind-SelectedIndex="@SelectedIndex" XAxisLabels="@XAxisLabels"></HeatMap>
            }
        </div>
    </CascadingValue>
</CascadingValue>
