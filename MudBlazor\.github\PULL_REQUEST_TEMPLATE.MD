<!--
<PERSON><PERSON> SURE TO READ THE CONTRIBUTING GUIDE BEFORE CREATING A PR
https://github.com/MudBlazor/MudBlazor/blob/dev/CONTRIBUTING.md

Testing sections can be removed for documentation changes
-->

<!-- Provide a general summary of your changes in the Title above -->
<!-- Keep the title short and descriptive, as it will be used as a commit message -->

## Description
<!-- Describe your changes in detail and why. -->
<!-- Note any issues that are resolved by this PR -->
<!-- e.g. resolves #1337 or fixes #9310. -->

## How Has This Been Tested?
<!-- All PRs should implement unit tests if possible. -->
<!-- Please describe how you tested your changes. -->
<!-- Have you created new tests or updated existing ones? -->
<!-- e.g. unit | visually | none -->

## Type of Changes
<!-- What type of changes does your code introduce? Put an `x` in only one box that applies best: -->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation (fix or improvement to the website or code docs)

<!-- If you made any visual changes, provide screenshots of before/after. If it has moving parts, please attach a high quality video. -->

## Checklist
<!-- Go over all the following points, and put an `x` in all the boxes that apply. -->
<!-- If you're unsure about any of these, don't hesitate to ask. We're here to help! -->
- [ ] The PR is submitted to the correct branch (`dev`).
- [ ] My code follows the code style of this project.
- [ ] I've added relevant tests.
