{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MudBlazor.UnitTests.Shared/1.0.0": {"dependencies": {"MudBlazor": "1.0.0", "bunit": "1.38.5", "NUnit": "4.2.2"}, "runtime": {"MudBlazor.UnitTests.Shared.dll": {}}}, "AngleSharp/1.2.0": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "AngleSharp.Css/1.0.0-beta.144": {"dependencies": {"AngleSharp": "1.2.0"}, "runtime": {"lib/net8.0/AngleSharp.Css.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "AngleSharp.Diffing/1.0.0": {"dependencies": {"AngleSharp": "1.2.0", "AngleSharp.Css": "1.0.0-beta.144"}, "runtime": {"lib/netstandard2.0/AngleSharp.Diffing.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "bunit/1.38.5": {"dependencies": {"bunit.core": "1.38.5", "bunit.web": "1.38.5"}}, "bunit.core/1.38.5": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Bunit.Core.dll": {"assemblyVersion": "1.38.5.407", "fileVersion": "1.38.5.407"}}}, "bunit.web/1.38.5": {"dependencies": {"AngleSharp": "1.2.0", "AngleSharp.Css": "1.0.0-beta.144", "AngleSharp.Diffing": "1.0.0", "Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.AspNetCore.Components.WebAssembly": "9.0.0", "Microsoft.AspNetCore.Components.WebAssembly.Authentication": "9.0.0", "Microsoft.Extensions.Caching.Memory": "9.0.0", "Microsoft.Extensions.Localization.Abstractions": "9.0.1", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "System.Text.Json": "9.0.0", "bunit.core": "1.38.5"}, "runtime": {"lib/net9.0/Bunit.Web.dll": {"assemblyVersion": "1.38.5.407", "fileVersion": "1.38.5.407"}}}, "Microsoft.AspNetCore.Authorization/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.1", "Microsoft.AspNetCore.Components.Analyzers": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.1": {}, "Microsoft.AspNetCore.Components.Authorization/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.1", "Microsoft.AspNetCore.Components": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Components.Forms/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.Web/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Forms": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1", "Microsoft.JSInterop": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.Extensions.Configuration.Binder": "9.0.0", "Microsoft.Extensions.Configuration.Json": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.JSInterop.WebAssembly": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Components.WebAssembly.Authentication/9.0.0": {"dependencies": {"Microsoft.AspNetCore.Components.Authorization": "9.0.0", "Microsoft.AspNetCore.Components.Web": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.Metadata/9.0.1": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileProviders.Physical": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.0", "Microsoft.Extensions.Configuration.Abstractions": "9.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.0", "Microsoft.Extensions.FileSystemGlobbing": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Localization/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Localization.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Primitives/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61010"}}}, "Microsoft.JSInterop/9.0.1": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.124.61009"}}}, "Microsoft.JSInterop.WebAssembly/9.0.0": {"dependencies": {"Microsoft.JSInterop": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.JSInterop.WebAssembly.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.24.52903"}}}, "NUnit/4.2.2": {"runtime": {"lib/net6.0/nunit.framework.dll": {"assemblyVersion": "4.2.2.0", "fileVersion": "4.2.2.0"}, "lib/net6.0/nunit.framework.legacy.dll": {"assemblyVersion": "4.2.2.0", "fileVersion": "4.2.2.0"}}}, "System.Text.Json/9.0.0": {}, "MudBlazor/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.Extensions.Localization": "9.0.1"}, "runtime": {"MudBlazor.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"MudBlazor.UnitTests.Shared/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AngleSharp/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-uF/PzSCVcb+b2nqVvHZbOqexoJ9R6QLjonugPf0PQl+0h7YKaFZeXyspctbHe5HGlx7/Iuk5BErtk+t63ac/ZA==", "path": "anglesharp/1.2.0", "hashPath": "anglesharp.1.2.0.nupkg.sha512"}, "AngleSharp.Css/1.0.0-beta.144": {"type": "package", "serviceable": true, "sha512": "sha512-WfyZ1zi5o7fNPgTv0O74nmzyxt9w4tjypwpOCSoeoZDOHtgghc/JqyGHRbQh7Y9sZlJiivQhrQNtm4XAy9LHYA==", "path": "anglesharp.css/1.0.0-beta.144", "hashPath": "anglesharp.css.1.0.0-beta.144.nupkg.sha512"}, "AngleSharp.Diffing/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6OeF2VvqyVaxMOP+wE0fjeaP+0ox2Og26tKDmY3Zf/qugRbd86OjmqoF6ZGyQonyP/zPjJ/TAB9VUR4HG3Dq5A==", "path": "anglesharp.diffing/1.0.0", "hashPath": "anglesharp.diffing.1.0.0.nupkg.sha512"}, "bunit/1.38.5": {"type": "package", "serviceable": true, "sha512": "sha512-QzUM6j3vX1cxGB79SaWOWVhP+fr0vrU/UYjjZdeuEIi59DMwAY35fz+ZPwW+z1AEoBaiMCje3BrBsjuS44y8cQ==", "path": "bunit/1.38.5", "hashPath": "bunit.1.38.5.nupkg.sha512"}, "bunit.core/1.38.5": {"type": "package", "serviceable": true, "sha512": "sha512-1tTNaTk2PIibXW/FohOESnIX7qt9HIYqdhJfzHiTGZcX9DQyoEkgqsstHKq/S3clZJdRU5wHDZA3nfhP7uSkpw==", "path": "bunit.core/1.38.5", "hashPath": "bunit.core.1.38.5.nupkg.sha512"}, "bunit.web/1.38.5": {"type": "package", "serviceable": true, "sha512": "sha512-uruHfxJrP5WTttKDv7V2+T9XaPgZExHLLIzqgl3n/Z45lfbFStfbmpIL+mNBwwItQKhpjHWDWjhlU0Vy9t6jvw==", "path": "bunit.web/1.38.5", "hashPath": "bunit.web.1.38.5.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-WgLlLBlMczb2+QLNG6sM95OUZ0EBztz60k/N75tjIgpyu0SdpIfYytAmX/7JJAjRTZF0c/CrWaQV+SH9FuGsrA==", "path": "microsoft.aspnetcore.authorization/9.0.1", "hashPath": "microsoft.aspnetcore.authorization.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6pwfbQKNtvPkbF4tCGiAKGyt6BVpu58xAXz7u2YXcUKTNmNxrymbG1mEyMc0EPzVdnquDDqTyfXM3mC1EJycxQ==", "path": "microsoft.aspnetcore.components/9.0.1", "hashPath": "microsoft.aspnetcore.components.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-I8Rs4LXT5UQxM5Nin2+Oj8aSY2heszSZ3EyTLgt3mxmfiRPrVO7D8NNSsf1voI2Gb0qFJceof/J5c9E+nfNuHw==", "path": "microsoft.aspnetcore.components.analyzers/9.0.1", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Authorization/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LD5ApnnUgMAyFDMKXqhyKFksnnxicGxE15dvC6rnOynFzj11Rvf7bENjTP9HUIbD64MYug+wlhl06A4nicw+RQ==", "path": "microsoft.aspnetcore.components.authorization/9.0.0", "hashPath": "microsoft.aspnetcore.components.authorization.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KyULVU32bLz74LWDwPEwNUEllTehzWJuM7YAsz80rMKEzvR0K8cRjRzO0fnN/nfydMeLRRlbI0xj8wnEAymLVw==", "path": "microsoft.aspnetcore.components.forms/9.0.1", "hashPath": "microsoft.aspnetcore.components.forms.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LI0vjYEd9MaDZPDQxPCn4gGYDkEC5U9rp1nWZo7rPozJxgTG2zU3WERujxTi2LeAC2ZzdXlOVCrUyPQ55LZV2A==", "path": "microsoft.aspnetcore.components.web/9.0.1", "hashPath": "microsoft.aspnetcore.components.web.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RwDygFGa1NdKh7N4S6P2xgxB0LeluLss/iZRdNnmOwN1zgpdLH9AnCnTIgDBAW7rhDjcPGrcjsDLNivwDRYwEQ==", "path": "microsoft.aspnetcore.components.webassembly/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Components.WebAssembly.Authentication/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dWGMHCh3/MchcPTvwz8FBd5O1FBE3Dxq6wWayi2xypgADJDTmpQnmMedN90sNVfekXQheAofYh0aPv6+Rt8Zlw==", "path": "microsoft.aspnetcore.components.webassembly.authentication/9.0.0", "hashPath": "microsoft.aspnetcore.components.webassembly.authentication.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EZnHifamF7IFEIyjAKMtJM3I/94OIe72i3P09v5oL0twmsmfQwal6Ni3m8lbB5mge3jWFhMozeW+rUdRSqnXRQ==", "path": "microsoft.aspnetcore.metadata/9.0.1", "hashPath": "microsoft.aspnetcore.metadata.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FPWZAa9c0H4dvOj351iR1jkUIs4u9ykL4Bm592yhjDyO5lCoWd+TMAHx2EMbarzUvCvgjWjJIoC6//Q9kH6YhA==", "path": "microsoft.extensions.caching.abstractions/9.0.0", "hashPath": "microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zbnPX/JQ0pETRSUG9fNPBvpIq42Aufvs15gGYyNIMhCun9yhmWihz0WgsI7bSDPjxWTKBf8oX/zv6v2uZ3W9OQ==", "path": "microsoft.extensions.caching.memory/9.0.0", "hashPath": "microsoft.extensions.caching.memory.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YIMO9T3JL8MeEXgVozKt2v79hquo/EFtnY0vgxmLnUvk1Rei/halI7kOWZL2RBeV9FMGzgM9LZA8CVaNwFMaNA==", "path": "microsoft.extensions.configuration/9.0.0", "hashPath": "microsoft.extensions.configuration.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lqvd7W3FGKUO1+ZoUEMaZ5XDJeWvjpy2/M/ptCGz3tXLD4HWVaSzjufsAsjemasBEg+2SxXVtYVvGt5r2nKDlg==", "path": "microsoft.extensions.configuration.abstractions/9.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RiScL99DcyngY9zJA2ROrri7Br8tn5N4hP4YNvGdTN/bvg1A3dwvDOxHnNZ3Im7x2SJ5i4LkX1uPiR/MfSFBLQ==", "path": "microsoft.extensions.configuration.binder/9.0.0", "hashPath": "microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4EK93Jcd2lQG4GY6PAw8jGss0ZzFP0vPc1J85mES5fKNuDTqgFXHba9onBw2s18fs3I4vdo2AWyfD1mPAxWSQQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WiTK0LrnsqmedrbzwL7f4ZUo+/wByqy2eKab39I380i2rd8ImfCRMrtkqJVGDmfqlkP/YzhckVOwPc5MPrSNpg==", "path": "microsoft.extensions.configuration.json/9.0.0", "hashPath": "microsoft.extensions.configuration.json.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uK439QzYR0q2emLVtYzwyK3x+T5bTY4yWsd/k/ZUS9LR6Sflp8MIdhGXW8kQCd86dQD4tLqvcbLkku8qHY263Q==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3+ZUSpOSmie+o8NnLIRqCxSh65XL/ExU7JYnFOg58awDRlY3lVpZ9A369jkoZL1rpsq7LDhEfkn2ghhGaY1y5Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jGFKZiXs2HNseK3NK/rfwHNNovER71jSj4BD1a/649ml9+h6oEtYd0GSALZDNW8jZ2Rh+oAeadOa6sagYW1F2A==", "path": "microsoft.extensions.filesystemglobbing/9.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UgvX4Yb2T3tEsKT30ktZr0H7kTRPapCgEH0bdTwxiEGSdA39/hAQMvvb+vgHpqmevDU5+puyI9ujRkmmbF946w==", "path": "microsoft.extensions.localization/9.0.1", "hashPath": "microsoft.extensions.localization.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CABog43lyaZQMjmlktuImCy6zmAzRBaXqN81uPaMQjlp//ISDVYItZPh6KWpWRF4MY/B67X5oDc3JTUpfdocZw==", "path": "microsoft.extensions.localization.abstractions/9.0.1", "hashPath": "microsoft.extensions.localization.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "Microsoft.JSInterop/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/xBwIfb0YoC2Muv6EsHjxpqZw2aKv94+i0g0FWZvqvGv3DeAy+8wipAuECVvKYEs2EIclRD41bjajHLoD6mTtw==", "path": "microsoft.jsinterop/9.0.1", "hashPath": "microsoft.jsinterop.9.0.1.nupkg.sha512"}, "Microsoft.JSInterop.WebAssembly/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5TjNuiavEVk+4UFTZVOKMlpq1qgYW/Vd6N7lNsHJR9kOlhP6S2GtiKdbLn//Aw/22lx3jDJTkekc54F5S8JC4g==", "path": "microsoft.jsinterop.webassembly/9.0.0", "hashPath": "microsoft.jsinterop.webassembly.9.0.0.nupkg.sha512"}, "NUnit/4.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-mon0OPko28yZ/foVXrhiUvq1LReaGsBdziumyyYGxV/pOE4q92fuYeN+AF+gEU5pCjzykcdBt5l7xobTaiBjsg==", "path": "nunit/4.2.2", "hashPath": "nunit.4.2.2.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "MudBlazor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}