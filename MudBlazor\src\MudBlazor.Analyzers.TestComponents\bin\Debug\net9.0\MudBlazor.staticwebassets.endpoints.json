{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "MudBlazor.min.bj7ppmloxe.js", "AssetFile": "MudBlazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}, {"Name": "label", "Value": "MudBlazor.min.js"}]}, {"Route": "MudBlazor.min.bj7ppmloxe.js", "AssetFile": "MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}, {"Name": "label", "Value": "MudBlazor.min.js"}]}, {"Route": "MudBlazor.min.bj7ppmloxe.js.gz", "AssetFile": "MudBlazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}, {"Name": "label", "Value": "MudBlazor.min.js.gz"}]}, {"Route": "MudBlazor.min.css", "AssetFile": "MudBlazor.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "MudBlazor.min.css", "AssetFile": "MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "MudBlazor.min.css.gz", "AssetFile": "MudBlazor.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}]}, {"Route": "MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "MudBlazor.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}, {"Name": "label", "Value": "MudBlazor.min.css"}]}, {"Route": "MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}, {"Name": "label", "Value": "MudBlazor.min.css"}]}, {"Route": "MudBlazor.min.hmu6kx9mgf.css.gz", "AssetFile": "MudBlazor.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}, {"Name": "label", "Value": "MudBlazor.min.css.gz"}]}, {"Route": "MudBlazor.min.js", "AssetFile": "MudBlazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "MudBlazor.min.js", "AssetFile": "MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "MudBlazor.min.js.gz", "AssetFile": "MudBlazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}]}]}