C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\bin\Release\net8.0\MudBlazor.staticwebassets.runtime.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\bin\Release\net8.0\MudBlazor.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\bin\Release\net8.0\MudBlazor.deps.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\bin\Release\net8.0\MudBlazor.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\bin\Release\net8.0\MudBlazor.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\bin\Release\net8.0\MudBlazor.xml
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\rpswa.dswa.cache.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.Resources.LanguageResource.resources
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.csproj.GenerateResource.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.AssemblyInfo.cs
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.sourcelink.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\rjimswa.dswa.cache.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\scopedcss\bundle\MudBlazor.styles.css
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets.build.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets.development.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets\msbuild.MudBlazor.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets\msbuild.MudBlazor.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets\msbuild.build.MudBlazor.props
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets\msbuild.buildMultiTargeting.MudBlazor.props
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets\msbuild.buildTransitive.MudBlazor.props
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets.pack.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\refint\MudBlazor.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.xml
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\MudBlazor.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor\obj\Release\net8.0\ref\MudBlazor.dll
