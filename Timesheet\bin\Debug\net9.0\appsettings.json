{"ConnectionStrings": {"dbInformatique": "Data Source=SQL14;Initial Catalog=Informatique;User ID=rapport;Password=rapport;app=PexNet;", "dbPEX": "Data Source=SQL14;Initial Catalog=Polyexpert;User ID=rapport;Password=rapport;app=PexNet;", "dbPEXReport": "Data Source=SQL14;Initial Catalog=PolyexpertReport;User ID=rapport;Password=rapport;app=PexNet;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Kestrel": {"Endpoints": {"Https": {"Url": "https://ti12.polyexpert.com:5002", "Certificate": {"Path": "C:\\Certbot\\live\\polyexpert.com\\polyexpert.com.pfx", "Password": "--1qaz--"}}}}, "Authentication": {"Microsoft": {"ClientId": "ea3c3eff-66c1-47f0-ba0e-7e07be829dc8", "ClientSecret": "****************************************", "AuthorizationEndpoint": "https://login.microsoftonline.com/c57678af-2274-48d1-a2ab-882a1ed3a4df/oauth2/v2.0/authorize", "TokenEndpoint": "https://login.microsoftonline.com/c57678af-2274-48d1-a2ab-882a1ed3a4df/oauth2/v2.0/token"}}, "Monday": {"ApiToken": "*********************************************************************************************************************************************************************************************************************************", "BoardId": 1872012298}}