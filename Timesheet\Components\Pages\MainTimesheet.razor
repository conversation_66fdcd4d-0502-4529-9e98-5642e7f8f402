@page "/Timesheet"
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject EmployeeService EmployeeService
@inject PexUserService PexUserService
@inject TimesheetEntryService TimesheetService
@inject IJSRuntime JS
@inject ProjectTaskService ProjectTaskService
@inject TaskTypeService TaskTypeService
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@inject AvatarService AvatarSvc


@using Timesheet.Data.Models
@using MudBlazor

@rendermode InteractiveServer

<style>
    .align-right input {
    text-align: right;
    }

    .mud-data-grid .mud-table-cell .column-header {
    display: block;
    align-items: center;
    }

    .DescriptionHeader {
    border-right: 1px solid;
    border-right-color: lightgrey;
    }

    .mud-table-child-content {
    padding: 0px !important;
    }

    .expander {
    width: 100%
    }

    .parentDataGrid th:first-child,
    .parentDataGrid td:first-child {
    width: 30px !important;
    max-width: 30px !important;
    min-width: 30px !important;
    }

    .ChildDataGrid th:first-child,
    .ChildDataGrid td:first-child {
    width: 147px !important;
    max-width: 147px !important;
    min-width: 147px !important;
    }

    .edit-mode-cell {
    padding-right: 16px !important;
    }

    tfoot.aggregates tr td {
    text-align: right;
    }

    .parentDataGrid .expanded-parent .hours-cell {
    color: transparent;
    }

    .parentDataGrid th,
    .ChildDataGrid th {
    white-space: pre-line;
    line-height: 1.1;
    }

    .scroll-container {
    overflow-x: auto;
    width: 100%;
    }

    .mud-data-grid {
    table-layout: fixed !important;
    min-width: 1200px !important;
    }

    /* colour only the hierarchy column (first cell in every body row) */
    .parentDataGrid tbody tr > td:first-child,
    .parentDataGrid tbody tr > td[tag="hierarchy-column"],
    .parentDataGrid tbody tr > td[data-tag="hierarchy-column"] {
    background-color: var(--hier-bg);
    }

    .parentDataGrid 
    > .mud-table-container > .mud-drop-container > .mud-table-root > tbody > tr > td:nth-child(1) {
    border-top-left-radius:25px;
    }

    .parentDataGrid > .mud-table-container > .mud-drop-container > .mud-table-root > tbody > tr > td:nth-child(2) > p {
    font-weight: bold;
    }

    .parentDataGrid > .mud-table-container > .mud-drop-container > .mud-table-root > tbody > tr {
    background: linear-gradient(to right, var(--hier-bg) 0%, #fff 100%);

    }

    .employeeSelector > .mud-input-control-input-container {
    width:400px;
    }

</style>

<MudPopoverProvider @rendermode="InteractiveServer" />
<MudDialogProvider />

<AuthorizeView>
    <Authorized>
        <div style="overflow-x:auto;">
            <div style="min-width: 1200px;">
                <MudGrid Justify="Justify.SpaceBetween">
                    <MudItem xs="3">
                        <MudStack Row="true">
                            <MudSelect T="Employee" customZIndex="5" Class="employeeSelector"
                            MultiSelection="true" Placeholder="Select Employees"
                            SelectedValues="_selectedEmployees"
                            SelectedValuesChanged="OnEmployeeSelectionChanged"
                            ToStringFunc="e => e != null ? e.PRENOM + ' ' + e.NOM : string.Empty">

                                @foreach (var employee in _allEmployees)
                                {
                                    <MudSelectItem T="Employee" Value="@employee">
                                        @employee.PRENOM @employee.NOM
                                    </MudSelectItem>
                                }
                            </MudSelect>

                            <!-- avatar bar directly under the <MudSelect> ------------------------- -->
                            <MudAvatarGroup >
                                @foreach (var emp in _selectedEmployees)
                                {
                                    <MudAvatar>
                                        <MudImage Src="@emp.PhotoBase64" />
                                    </MudAvatar>
                                }
                            </MudAvatarGroup>

                        </MudStack>
                    </MudItem>

                    <MudItem xs="5">
                        <MudStack  Row="true" AlignItems="AlignItems.Center" StretchItems="StretchItems.None" Style="margin-left:20px; width:500px;" Justify="Justify.Center">
                            <MudIconButton Style="padding-bottom:0; padding-top:0;" Icon="@Icons.Material.Filled.ChevronLeft" OnClick="@PreviousWeek" />
                            <MudDatePicker Culture="@(new CultureInfo("fr-FR"))" Style="padding-bottom:0; padding-top:0" DateFormat="'Semaine du' dd MMMM yyyy" Date="_selectedDate" DateChanged="OnDateChanged" />
                            <MudIconButton Style="padding-bottom:0; padding-top:0;" Icon="@Icons.Material.Filled.ChevronRight" OnClick="@NextWeek" />
                            <MudSelect T="int"
                                       Style="width:150px; margin-left:10px;"
                                       Label="Semaines précédentes"
                                       Value="_previousWeeksToLoad"
                                       ValueChanged="OnPreviousWeeksChanged"
                                       Variant="Variant.Outlined"
                                       Margin="Margin.Dense">
                                <MudSelectItem T="int" Value="0">Aucune</MudSelectItem>
                                <MudSelectItem T="int" Value="1">1 semaine</MudSelectItem>
                                <MudSelectItem T="int" Value="2">2 semaines</MudSelectItem>
                                <MudSelectItem T="int" Value="3">3 semaines</MudSelectItem>
                                <MudSelectItem T="int" Value="4">4 semaines</MudSelectItem>
                            </MudSelect>
                        </MudStack>
                    </MudItem>

                    <MudItem xs="1">

                        <MudStack Row="true" Justify="Justify.FlexEnd">

                            <MudItem>
                                <MudIconButton Style="padding:4px 4px 4px 4px !important; display: none;" Icon="@Icons.Material.Filled.Add" OnClick="createTask" />

                                <MudOverlay @bind-Visible="@createTaskPopOpen" OnClick="CloseCreateTaskPopOver" />
                                <MudPopover TransformOrigin="Origin.BottomRight" AnchorOrigin="Origin.BottomCenter" Open="@createTaskPopOpen">
                                    <MudCard Style="width:500px;">
                                        <MudCardHeader>
                                            <CardHeaderContent>

                                                <MudText Typo="Typo.h6">Create a task</MudText>

                                            </CardHeaderContent>
                                        </MudCardHeader>

                                        <MudCardContent>
                                            <MudStack Spacing="5">
                                                <MudSelect customZIndex="1602" T="Employee" Label="Employee" @ref="employeeSelector" DropdownSettings="new() { Fixed = false }"
                                                @bind-Value="_selectedEmployee"
                                                ToStringFunc="e => e != null ? e.PRENOM + ' ' + e.NOM : string.Empty">
                                                    @foreach (var employee in _selectedEmployees)
                                                    {
                                                        <MudSelectItem T="Employee" Value="@employee">
                                                            @employee.PRENOM @employee.NOM
                                                        </MudSelectItem>
                                                    }

                                                </MudSelect>
                                                <MudTextField 
                                                @bind-Value="_taskToCreateName"
                                                Label="Task name" />

                                                <MudAutocomplete T="ProjectTask"
                                                customZIndex="1602"
                                                @bind-Value="_selectedTask"
                                                SearchFunc="SearchTasks"
                                                ToStringFunc="task => task != null ? task.NAME : string.Empty"
                                                Label="Task"
                                                Clearable="true" />

                                            </MudStack>

                                        </MudCardContent>

                                        <MudCardActions>
                                            <MudGrid Justify="Justify.FlexEnd">
                                                <MudItem>
                                                    <MudButton Color="Color.Tertiary" OnClick="CloseCreateTaskPopOver">
                                                        Cancel
                                                    </MudButton>
                                                    <MudButton Color="Color.Primary" OnClick="CreateNewTask">
                                                        Add
                                                    </MudButton>
                                                </MudItem>

                                            </MudGrid>

                                        </MudCardActions>

                                    </MudCard>
                                </MudPopover>
                            </MudItem>

                            <MudItem>
                                <MudIconButton Style="padding:4px 4px 4px 4px !important"
                                Icon="@Icons.Material.Filled.Add"
                                OnClick="@OpenAddTaskDialog" />

                                <MudOverlay @bind-Visible="@addTaskPopOpen" AutoClose="true"/>
                                <MudPopover TransformOrigin="Origin.BottomRight" AnchorOrigin="Origin.BottomCenter" Open="@addTaskPopOpen">
                                    <MudCard Style="width:500px;">
                                        <MudCardHeader>
                                            <CardHeaderContent>

                                                <MudText Typo="Typo.h6">Search and add an existing task</MudText>

                                            </CardHeaderContent>
                                        </MudCardHeader>

                                        <MudCardContent>
                                            <MudStack Spacing="5">
                                                <MudSelect customZIndex="1602" T="Employee"
                                                Label="Employee"
                                                @bind-Value="_selectedEmployee"
                                                ToStringFunc="e => e != null ? e.PRENOM + ' ' + e.NOM : string.Empty">
                                                    @foreach (var employee in _selectedEmployees)
                                                    {
                                                        <MudSelectItem T="Employee" Value="@employee">
                                                            @employee.PRENOM @employee.NOM
                                                        </MudSelectItem>
                                                    }

                                                </MudSelect>
                                                <MudSelect T="TaskType"
                                                Label="Task type"
                                                @bind-Value="_selectedTaskTypeForFiltering"
                                                ToStringFunc="t => t != null ? t.TypeName : string.Empty"
                                                Clearable="true"
                                                CustomZIndex="1602">
                                                    @foreach (var type in _taskTypes)
                                                    {
                                                        <MudSelectItem T="TaskType" Value="@type">
                                                            @type.TypeName
                                                        </MudSelectItem>
                                                    }
                                                </MudSelect>

                                                <MudAutocomplete T="ProjectTask" customZIndex="1602"
                                                @bind-Value="_selectedTask"
                                                SearchFunc="SearchTasks"
                                                ToStringFunc="task => task != null ? task.NAME : string.Empty"
                                                Label="Task"
                                                Clearable="true" Disabled=@(_selectedTaskTypeForFiltering == null) />

                                            </MudStack>

                                        </MudCardContent>

                                        <MudCardActions>
                                            <MudGrid Justify="Justify.FlexEnd">
                                                <MudItem>
                                                    <MudButton Color="Color.Tertiary" OnClick="@(() => addTaskPopOpen = false)">
                                                        Cancel
                                                    </MudButton>
                                                    <MudButton Color="Color.Primary" OnClick="AddTaskConfirmed">
                                                        Add
                                                    </MudButton>
                                                </MudItem>
                                            </MudGrid>
                                        </MudCardActions>
                                    </MudCard>
                                </MudPopover>
                            </MudItem>

                            <MudItem>


                                <MudIconButton Icon="@Icons.Material.Filled.SwapVert"
                                Style="padding:4px !important"
                                OnClick="OpenReorderDialog" />


                            </MudItem>
                        </MudStack>
                    </MudItem>
                </MudGrid>

                <MudDataGrid ShowSpacerRow="true"  Breakpoint="Breakpoint.None" RowClassFunc="@( (row, _) =>
                 _myDataGrid != null && _myDataGrid._openHierarchies.Contains(row)
                     ? "expanded-parent" 
                     : null )" RowStyleFunc="GetRowStyle" RowClass="spaced-row no-side-borders" FixedHeader Height="800px" @ref="_myDataGrid" Class="no-grid-frame parentDataGrid" T="WeekEntriesGroup" Items="@_weekEntriesGroups"
                ShowColumnOptions="false" SortMode="SortMode.None"
                ColumnResizeMode="MudBlazor.ResizeMode.None"
                Bordered="false" ReadOnly="true" Dense="false" FixedFooter="true" FooterClass="aggregates" RowClick="OnParentRowClick">
                    <Columns>
                        <HierarchyColumn T="WeekEntriesGroup" CellStyleFunc="HierarchyCellStyle" />

                        <TemplateColumn Context="cellContext"
                        HeaderStyle="padding-bottom:0!important;"
                        CellStyle="width:350px;min-width:350px;max-width:350px;">
                            <HeaderTemplate>
                                <div style="width:400px;height:36px;background:#fff;"></div>
                            </HeaderTemplate>

                            <CellTemplate>
                                <MudText Typo="Typo.body2">
                                    @cellContext.Item.TaskType.TypeName
                                </MudText>
                            </CellTemplate>
                        </TemplateColumn>


                        <PropertyColumn Property="x => x.TotalSundayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(0)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />

                        <PropertyColumn Property="x => x.TotalMondayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(1)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />

                        <PropertyColumn Property="x => x.TotalTuesdayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(2)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />

                        <PropertyColumn Property="x => x.TotalWednesdayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(3)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />

                        <PropertyColumn Property="x => x.TotalThursdayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(4)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />

                        <PropertyColumn Property="x => x.TotalFridayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(5)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />

                        <PropertyColumn Property="x => x.TotalSaturdayHours"
                        CellClass="hours-cell"
                        Title="@DayHeader(6)"
                        Format="0.00"
                        CellStyle="width:100px;min-width:100px;max-width:100px;text-align:right"
                        HeaderStyle="text-align:right;"
                        AggregateDefinition="@(_hoursAggregation)" />
                    </Columns>

                    <!-- Use the child component in the ChildRowContent -->
                    <ChildRowContent Context="gridContext">
                        <TimesheetWeekEntriesGrid OnTimesheetEntryChanged="HandleTimesheetEntryChanged"
                        WeekEntries="@gridContext.Item.Entries" AllEmployees="@_allEmployees"
                        WeekStart="@WeekStart" />
                    </ChildRowContent>
                </MudDataGrid>
            </div>
        </div>
    </Authorized>
    <NotAuthorized>
        <MicrosoftExternalLoginRedirect />
    </NotAuthorized>
</AuthorizeView>

@code {
    private Employee? _userEmployee;
    private PexUser? _pexUser;
    private List<Employee> _selectedEmployees = new();
    private string? _entraId;
    private List<TimesheetEntry> _timesheetEntries = new List<TimesheetEntry>();
    private List<WeekEntries> _weekEntries = new List<WeekEntries>();
    private List<WeekEntriesGroup> _weekEntriesGroups = new List<WeekEntriesGroup>();
    private List<Employee> _allEmployees = new();
    private bool addTaskPopOpen = false;
    private bool createTaskPopOpen = false;
    private Employee? _selectedEmployee;
    private List<ProjectTask> _allTasks = new();
    private ProjectTask? _selectedTask;
    private MudDataGrid<WeekEntriesGroup>? _myDataGrid;
    private string? _taskToCreateName;
    private string? _customTaskTypeNameCreationInput;
    private List<TaskType> _taskTypes = new List<TaskType>();
    private TaskType? _selectedTaskTypeForCreation;
    private MudSelect<Employee>? employeeSelector;
    private readonly CultureInfo _fr = new("fr-FR");

    private TaskType? _selectedTaskTypeForFiltering
    {
        get => _taskTypeFilterBacking;
        set
        {
            if (_taskTypeFilterBacking != value)
            {
                _taskTypeFilterBacking = value;
                _selectedTask = null; // Clear selected task when type changes
            }
        }
    }
    private TaskType? _taskTypeFilterBacking;


    private DateTime? _selectedDate = DateTime.Today;

    // Configuration for how many previous weeks to load tasks from
    private int _previousWeeksToLoad = 1;


    private async Task OpenReorderDialog()
    {
        var dlg = await DialogService.ShowAsync<ReorderDialog>(
            "Re-order task types",
            new DialogParameters { ["Types"] = _taskTypes },
            new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true });

        var result = await dlg.Result;
        if (result.Canceled || result.Data is not List<TaskType> newOrder)
            return;                                       // user pressed Cancel

        await TaskTypeService.UpdateOrderAsync(newOrder);

        _taskTypes = newOrder;

        _weekEntriesGroups = _weekEntriesGroups
            .OrderBy(g => _taskTypes.FindIndex(t => t.Id == g.TaskType.Id))
            .ToList();

        StateHasChanged();
    }


    private async Task OpenDialogAsync(WeekEntriesGroup row)
    {
        var parameters = new DialogParameters
            {
                ["Row"] = row,
                ["AllProjectTasks"] = _allTasks
            };

        var options = new DialogOptions { CloseOnEscapeKey = true };

        // ① open the dialog and keep the reference
        var dlgRef = await DialogService.ShowAsync<Dialog>(
                               "Modifier le groupe", parameters, options);

        // ② wait until the dialog is closed
        var dlgResult = await dlgRef.Result;          // ←-- this returns only AFTER OK / Cancel

        // ③ if the user pressed OK, force a re-render of this page
        if (!dlgResult.Canceled)
            StateHasChanged();
    }




    private async Task OnParentRowClick(DataGridRowClickEventArgs<WeekEntriesGroup> args)
    {
        if (args.MouseEventArgs?.Detail == 2)
            await OpenDialogAsync(args.Item);
    }

    private string DayHeader(int offset)
    {
        var d = WeekStart.AddDays(offset);
        return $"{d.ToString("dddd", _fr)}\n{d:dd}";
    }

    @code {
        private static string GetRowStyle(WeekEntriesGroup row, int _)
        {
            var hex = row.TaskType?.Couleur;
            if (string.IsNullOrWhiteSpace(hex)) return string.Empty;

            // add 20 % transparency when the value is #RRGGBB
            var bg = hex.Length == 7 ? $"{hex}33" : hex;

            // expose the value – but DON’T set background-color here
            return $"--hier-bg:{bg};";
        }
    }


    private DateTime WeekStart => (_selectedDate ?? DateTime.Today).AddDays(-((int)(_selectedDate ?? DateTime.Today).DayOfWeek));

    private readonly AggregateDefinition<WeekEntriesGroup> _hoursAggregation =
    new()
            {
                Type = AggregateType.Sum,
                NumberFormat = "0.00"
            };

    private async Task<IEnumerable<TaskType>> SearchTaskTypes(string value, CancellationToken cancellationToken)
    {
    if (string.IsNullOrWhiteSpace(value))
    {
        return _taskTypes.AsEnumerable();
    }

    await Task.Delay(5, cancellationToken);
    cancellationToken.ThrowIfCancellationRequested();

    return _taskTypes.Where(c => $"{c.TypeName}".Contains(value, StringComparison.InvariantCultureIgnoreCase));
    }

    private async Task CreateNewTask()
    {

    if (_selectedEmployee != null && _taskToCreateName != null && !string.IsNullOrWhiteSpace(_customTaskTypeNameCreationInput) || _selectedTaskTypeForCreation != null)
    {
        if (_selectedTaskTypeForCreation != null)
        {
            await AddNewTaskToGridAsync(
                _taskToCreateName,
                _selectedTaskTypeForCreation,
                _selectedEmployee);

            await CloseCreateTaskPopOver();
        } 
        else
        {
            var newType = await TaskTypeService
                .CreateNewType(_customTaskTypeNameCreationInput);

            var namePrefix = new string(
                _taskToCreateName
                    .Where(char.IsLetter)
                    .Take(3)
                    .ToArray()
            )
            .ToUpper();

            var firstID = $"{namePrefix}-{1:D3}";

            var newTask = new ProjectTask
                    {
                        GIVEN_TASK_ID = firstID,
                        NAME = _taskToCreateName,
                        //TypeId = newType.Id
                    };
            var createdTask = await ProjectTaskService.InsertTaskAsync(newTask);

            await AddNewTaskToGridAsync(
                createdTask.NAME,
                newType,
                _selectedEmployee);

            await CloseCreateTaskPopOver();
        }

    }

    }

    public async Task AddNewTaskToGridAsync(
    string taskName,
    TaskType selectedTaskType,
    Employee currentEmployee)
    {
    if (selectedTaskType == null
        || string.IsNullOrWhiteSpace(selectedTaskType.TypeName))
    {
        throw new ArgumentException(
            "Selected TaskType must be provided and have a valid TypeName.",
            nameof(selectedTaskType));
    }

        var newTask = await ProjectTaskService
        .CreateProjectTaskAsync(taskName, selectedTaskType);

        @* newTask.TaskType = selectedTaskType; *@

        var newEntry = new WeekEntries
            {
                ProjectTask = newTask,
                employee = currentEmployee,
            };

        AddWeekEntry(newEntry);
        OpenGroup(newEntry);
        StateHasChanged();
    }

    private void OpenGroup(WeekEntries entry)
    {
        if (_myDataGrid is null) return;

        var match = _weekEntriesGroups
            .FirstOrDefault(g => g.TaskType.Id == entry.TaskType.Id);

        if (match is not null && !_myDataGrid._openHierarchies.Contains(match))
            _myDataGrid._openHierarchies.Add(match);
    }

    private void AddWeekEntry(WeekEntries entry)
    {
    _weekEntries.Add(entry);

    var group = _weekEntriesGroups
        .FirstOrDefault(g => g.TaskType.Id == entry.TaskType.Id);

    if (group == null)
    {
        group = new WeekEntriesGroup { TaskType = entry.TaskType! };
        _weekEntriesGroups.Add(group);
    }

    group.Entries.Add(entry);

    }



    private async Task CloseCreateTaskPopOver()
    {

    _selectedTaskTypeForCreation = null;
    _selectedEmployee = null;
    _customTaskTypeNameCreationInput = null;
    _taskToCreateName = null;
    createTaskPopOpen = false;

    }

    private async Task AddTaskConfirmed()
    {
    // Ensure both a task and an employee are selected.
    if (_selectedEmployee == null || _selectedTask == null)
    {
        // Optionally show a message or return.
        return;
    }

    // Check if the combination of selected employee and task already exists.
    bool alreadyExists = _weekEntries.Any(we =>
        we.employee.ID_EMPLOYE == _selectedEmployee.ID_EMPLOYE &&
        we.ProjectTask.GIVEN_TASK_ID == _selectedTask.GIVEN_TASK_ID);

    if (alreadyExists)
    {
        this.Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
        Snackbar.Add("This task is already assigned to the selected employee.", Severity.Error);

        return;
    }

    // Create a new WeekEntries instance for the selected task and employee.
    var newWeekEntry = new WeekEntries
            {
                employee = _selectedEmployee,
                ProjectTask = _selectedTask,
                sunday = null,
                monday = null,
                tuesday = null,
                wednesday = null,
                thursday = null,
                friday = null,
                saturday = null
            };

    AddWeekEntry(newWeekEntry);
    addTaskPopOpen = false;
    OpenGroup(newWeekEntry);
    StateHasChanged();
    }


private async Task<IEnumerable<ProjectTask>> SearchTasks(string value, CancellationToken token)
{
    await Task.Delay(10, token);

    if (string.IsNullOrWhiteSpace(value))
        return _allTasks;

    return _allTasks.Where(task =>
               // NAME match
               (!string.IsNullOrWhiteSpace(task.NAME) &&
                task.NAME.Contains(value, StringComparison.InvariantCultureIgnoreCase))
            || // OR GIVEN_TASK_ID match
               (!string.IsNullOrWhiteSpace(task.GIVEN_TASK_ID) &&
                task.GIVEN_TASK_ID.Contains(value, StringComparison.InvariantCultureIgnoreCase)));
}






    protected override async Task OnInitializedAsync()
    {
    var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
    var user = authState.User;

    if (user.Identity?.IsAuthenticated == true)
    {
        _entraId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!string.IsNullOrEmpty(_entraId))
        {
            try
            {
                _userEmployee = await EmployeeService.GetEmployeeByEntraIdAsync(_entraId);
                _allEmployees = (await EmployeeService.GetRnDEmployeesAsync()).ToList();

                // Timesheet.razor  – add this right after:
                // _allEmployees = (await EmployeeService.GetRnDEmployeesAsync()).ToList();

                if (_allEmployees.Count != 0)
                {
                    // ① Ask AvatarService for every employee’s picture (by e-mail)
                    //    – the service already returns a base-64 string like the one you
                    //      stored in Employee.PhotoBase64.
                    var fetchTasks = _allEmployees
                        .Where(e => !string.IsNullOrWhiteSpace(e.EMAIL))
                        .Select(async emp =>
                        {
                            try
                            {
                                emp.PhotoBase64 = await AvatarSvc.GetUserPhotoAsync(emp.EMAIL!);
                            }
                            catch
                            {
                                // optional: swallow 404 / Graph “no photo” errors
                                emp.PhotoBase64 = null;
                            }
                        });

                    // ② Wait until they’re all done (you can throttle, but 10–20 in parallel
                    //    is perfectly fine against Graph or your own API).
                    await Task.WhenAll(fetchTasks);
                }


                _allTasks = await ProjectTaskService.GetAllTasksAsync();

                _taskTypes = await TaskTypeService.GetAllTaskTypesAsync();

                _taskTypes = _taskTypes
                     .OrderBy(t => t.Ordre ?? int.MaxValue)
                     .ToList();

                if (_userEmployee?.ID_PEX_USER != null)
                {
                    _pexUser = await PexUserService.GetPexUserByEmployeeAsync(_userEmployee.ID_PEX_USER);

                    var matchedEmployee = _allEmployees.FirstOrDefault(e => e.ID_EMPLOYE == _userEmployee.ID_EMPLOYE);

                    if (matchedEmployee != null)
                    {
                        _selectedEmployees = new List<Employee> { matchedEmployee };
                        await LoadDataAsync(_selectedEmployees);
                    }

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error fetching data: {ex.Message}");
            }
        }
    }
    }


    private async Task OnEmployeeSelectionChanged(IEnumerable<Employee> newSelectedEmployees)
    {
    _selectedEmployees = newSelectedEmployees.ToList();
    await LoadDataAsync(_selectedEmployees);
    }



        private Task<List<WeekEntries>> ConvertToWeekEntries(IEnumerable<TimesheetEntry> entries)
        {
    var result = new List<WeekEntries>();

    // 1️⃣ group by employee + project-task
    var grouped = entries.GroupBy(e => new { e.IdEmployee, e.IdProjectTask });

    foreach (var grp in grouped)
    {
        /* ── employee ─────────────────────────────────────────── */
        var employee = _allEmployees
            .FirstOrDefault(emp => emp.ID_EMPLOYE == grp.Key.IdEmployee);
        if (employee is null)
            continue;                               // skip rows with an unknown employee

        /* ── task ─────────────────────────────────────────────── */
        var projectTask = _allTasks
            .FirstOrDefault(t => t.ID_TASK == grp.Key.IdProjectTask);
        if (projectTask is null)
            continue;                               // skip rows with an unknown task

        /* ── task type (can be null) ─────────────────────────── */
        var firstEntry = grp.First();          // any row from the group is fine
        TaskType? taskType = _taskTypes
            .FirstOrDefault(tt => tt.Id == firstEntry.IdProjectTaskType);

        /* ── build WeekEntries ───────────────────────────────── */
        var week = new WeekEntries
                {
                    employee = employee,
                    ProjectTask = projectTask,
                    TaskType = taskType
                };

        foreach (var entry in grp)
        {
            switch ((int)entry.WorkDate.DayOfWeek)
            {
                case 0: week.sunday = entry; break;
                case 1: week.monday = entry; break;
                case 2: week.tuesday = entry; break;
                case 3: week.wednesday = entry; break;
                case 4: week.thursday = entry; break;
                case 5: week.friday = entry; break;
                case 6: week.saturday = entry; break;
            }
        }

        result.Add(week);
    }

    // still return a Task so existing 'await' sites compile
    return Task.FromResult(result);
        }



    private async Task LoadDataAsync(List<Employee> employees)
    {
    // 1) remember which groups were open
    var previouslyOpenGroups = _myDataGrid._openHierarchies.ToList();

    // 2) clear out if no employees
    if (employees == null || employees.Count == 0)
    {
        _timesheetEntries = new List<TimesheetEntry>();
        _weekEntries = new List<WeekEntries>();
        _weekEntriesGroups = new List<WeekEntriesGroup>();
        StateHasChanged();
        return;
    }




    // 3) determine this week’s and last week’s start dates
    var thisWeekStart = WeekStart;


    // 4) fetch raw TimesheetEntry rows
    var thisWeekRaw = new List<TimesheetEntry>();

    foreach (var emp in employees)
    {
        // your existing service method takes a “reference date” and returns that calendar week
        var entriesThis = await TimesheetService
            .GetWeekTimesheetEntriesAsync(emp.ID_EMPLOYE, thisWeekStart);
        thisWeekRaw.AddRange(entriesThis);


    }

    // 5) convert current week to WeekEntries objects
    var thisWeekEntries = await ConvertToWeekEntries(thisWeekRaw);

    // 6) fetch and merge tasks from previous weeks if configured
    if (_previousWeeksToLoad > 0)
    {
        var allPreviousWeekEntries = new List<WeekEntries>();

        // Fetch tasks from each previous week
        for (int weekOffset = 1; weekOffset <= _previousWeeksToLoad; weekOffset++)
        {
            var previousWeekStart = thisWeekStart.AddDays(-7 * weekOffset);
            var previousWeekRaw = new List<TimesheetEntry>();

            foreach (var emp in employees)
            {
                var entriesPrevious = await TimesheetService
                    .GetWeekTimesheetEntriesAsync(emp.ID_EMPLOYE, previousWeekStart);
                previousWeekRaw.AddRange(entriesPrevious);
            }

            var previousWeekEntries = await ConvertToWeekEntries(previousWeekRaw);
            allPreviousWeekEntries.AddRange(previousWeekEntries);
        }

        // Add tasks from previous weeks that don't exist in current week
        foreach (var pw in allPreviousWeekEntries)
        {
            bool exists = thisWeekEntries.Any(w =>
                w.employee.ID_EMPLOYE == pw.employee.ID_EMPLOYE &&
                w.ProjectTask.GIVEN_TASK_ID == pw.ProjectTask.GIVEN_TASK_ID);

            if (!exists)
            {
                var blank = new WeekEntries
                {
                    employee = pw.employee,
                    ProjectTask = pw.ProjectTask,
                    TaskType = pw.TaskType
                };

                thisWeekEntries.Add(blank);
            }
        }
    }

    // 6) for any (emp,task) in lastWeek but NOT in thisWeek, add an “empty” WeekEntries
    // calculate start of today’s week


    // only add last week’s tasks if viewing current week




    _weekEntries = thisWeekEntries;

    _weekEntriesGroups = _weekEntries
        .GroupBy(w => w.TaskType!.Id)
        .Select(g =>
        {
            var sharedType = g.First().TaskType!;
            foreach (var w in g)
                w.TaskType = sharedType;

            return new WeekEntriesGroup
                    {
                        TaskType = sharedType,
                        Entries = g.ToList()
                    };
        })
        .OrderBy(g => _taskTypes.FindIndex(t => t.Id == g.TaskType.Id))
        .ToList();



    // 8) restore open hierarchies
    _myDataGrid._openHierarchies.Clear();

    foreach (var old in previouslyOpenGroups)
    {
        // use the primary-key Id so it works even if the TaskType instances
        // are different objects that represent the same DB row
        var match = _weekEntriesGroups
            .FirstOrDefault(g => g.TaskType.Id == old.TaskType.Id);

        if (match is not null)
            _myDataGrid._openHierarchies.Add(match);
    }


    // 9) refresh
    StateHasChanged();
    }




    private async Task OnDateChanged(DateTime? newDate)
    {
    _selectedDate = newDate;
    await LoadDataAsync(_selectedEmployees);

    }

    private async Task OnPreviousWeeksChanged(int newValue)
    {
        _previousWeeksToLoad = newValue;
        await LoadDataAsync(_selectedEmployees);
    }

    private async Task PreviousWeek()
    {
    _selectedDate = (_selectedDate ?? DateTime.Today).AddDays(-7);
    await LoadDataAsync(_selectedEmployees);

    }

    private async Task addTask()
    {
    _selectedTask = null;

    // Auto-select the employee if there's exactly one in the list
    if (_selectedEmployees.Count == 1)
    {
        _selectedEmployee = _selectedEmployees.First();
    }
    else
    {
        _selectedEmployee = null;
    }

    addTaskPopOpen = true;
    }


    private async Task createTask()
    {
        _selectedEmployee = null;
        _selectedTask = null;
        createTaskPopOpen = true;
    }

    private async Task NextWeek()
    {
        _selectedDate = (_selectedDate ?? DateTime.Today).AddDays(7);
        await LoadDataAsync(_selectedEmployees);

    }

    private async Task HandleTimesheetEntryChanged()
    {
        StateHasChanged();
    }


        private async Task OpenAddTaskDialog()
        {
            var parameters = new DialogParameters
                    {
                        [nameof(AddTaskDialog.SelectedEmployees)] = _selectedEmployees,
                        [nameof(AddTaskDialog.TaskTypes)] = _taskTypes,
                        [nameof(AddTaskDialog.AllTasks)] = _allTasks
                    };

            var opts = new DialogOptions { MaxWidth = MaxWidth.Small, FullWidth = true };
            var dlgRef = DialogService.Show<AddTaskDialog>(
                             "Search and add an existing task", parameters, opts);

            var result = await dlgRef.Result;
            if (result.Canceled) return;

            // (Employee, ProjectTask?, TaskType, bool withoutTask, string? freeText)
            if (result.Data is not ValueTuple<Employee, ProjectTask?, TaskType, bool, string?> payload)
                return;                                               // defensive

            var (emp, pickedTask, type, withoutTask, freeText) = payload;


            ProjectTask task = pickedTask!;       // existing task by default

            if (withoutTask)
            {
                if (string.IsNullOrWhiteSpace(freeText))
                {
                    Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                    Snackbar.Add("Task name cannot be empty.", Severity.Error);
                    return;
                }

                // check if a task with the same name already exists (case-insensitive)
                var existingTask = _allTasks
                    .FirstOrDefault(t => string.Equals(t.NAME, freeText, StringComparison.OrdinalIgnoreCase));

                if (existingTask != null)
                {
                    Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                    Snackbar.Add("A task with this name already exists.", Severity.Error);
                    return;
                }

                // create & persist
                task = await ProjectTaskService.CreateProjectTaskAsync(freeText, type);

                // make it searchable in this session
                _allTasks.Add(task);

            }


            bool alreadyExists = _weekEntries.Any(w =>
                w.employee.ID_EMPLOYE == emp.ID_EMPLOYE &&
                w.TaskType.Id == type.Id &&
                w.ProjectTask.ID_TASK == task.ID_TASK);

            if (alreadyExists)
            {
                Snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomRight;
                Snackbar.Add("This task is already assigned to the employee.",
                             Severity.Error);
                return;
            }

            var newEntry = new WeekEntries
                    {
                        employee = emp,
                        ProjectTask = task,
                        TaskType = type
                    };

            AddWeekEntry(newEntry);
            OpenGroup(newEntry);
            StateHasChanged();
        }


}

