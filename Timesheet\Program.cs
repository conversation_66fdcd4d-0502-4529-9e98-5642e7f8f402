﻿using Timesheet.Components;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server;
using Timesheet.Data;
using Timesheet.Data.Services;
using MudBlazor.Services;
using Timesheet.Data.Models;
using Microsoft.Extensions.Hosting.WindowsServices;
using MudBlazor;
using Prometheus;


var builder = WebApplication.CreateBuilder(args);

builder.Services.AddScoped<AuthenticationStateProvider, ServerAuthenticationStateProvider>();


builder.Services.AddAuthentication("loginCookie")
    .AddCookie("loginCookie")
    .AddMicrosoftAccount(options =>
    {
        options.ClientId = builder.Configuration["Authentication:Microsoft:ClientId"];
        options.ClientSecret = builder.Configuration["Authentication:Microsoft:ClientSecret"];

        // these are already in your config
        options.AuthorizationEndpoint = builder.Configuration["Authentication:Microsoft:AuthorizationEndpoint"];
        options.TokenEndpoint = builder.Configuration["Authentication:Microsoft:TokenEndpoint"];

        /* NEW — ask Microsoft identity platform for Graph access */
        options.Scope.Add("User.Read");
        options.Scope.Add("User.ReadBasic.All");
        // or User.Read.All


        options.SaveTokens = true;      // keep the access token in the auth cookie
    });


builder.Services.AddHttpClient();
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<AvatarService>();


builder.Host.UseWindowsService();

builder.Services.AddCascadingAuthenticationState();

// Register database and services
builder.Services.AddSingleton<DatabaseFactory>();
builder.Services.AddSingleton<PetaPocoDatabaseFactory>();
builder.Services.AddScoped<EmployeeService>();
builder.Services.AddScoped<PexUserService>();
builder.Services.AddScoped<TreeService>();
builder.Services.AddScoped<PexTreeService>();
builder.Services.AddScoped<PexGroupService>();
builder.Services.AddScoped<TimesheetEntryService>();
builder.Services.AddScoped<ProjectTaskService>();
builder.Services.AddScoped<TaskTypeService>();
builder.Services.AddScoped<PVService>();
builder.Services.AddScoped<ListePrixService>();
builder.Services.AddScoped<ListePrixDetailService>();
builder.Services.AddScoped<StdProduitClientService>();
builder.Services.AddScoped<ProduitClientService>();
builder.Services.AddScoped<ProduitService>();
builder.Services.AddScoped<RnDLinkService>();
builder.Services.AddScoped<CommandeMachineService>();

builder.Services.AddHttpClient<MondayApiClient>();
builder.Services.Configure<MondayOptions>(
        builder.Configuration.GetSection("Monday"));

builder.Services.AddScoped<IMondaySyncOrchestrator, MondaySyncOrchestrator>();
#if !DEBUG
builder.Services.AddHostedService<MondaySyncHostedService>();
#endif


builder.Services.Configure<CircuitOptions>(options => options.DetailedErrors = true);
// Add MudBlazor services
builder.Services.AddMudServices();

builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddServerSideBlazor()
       .AddCircuitOptions(o => o.DetailedErrors = true);

builder.Services.AddMudServices(cfg =>
{
    cfg.SnackbarConfiguration.PositionClass =
        Defaults.Classes.Position.BottomRight;
});


var app = builder.Build();

if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseAntiforgery();

app.MapStaticAssets();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.MapIdentityEndpoints();

app.UseMetricServer();   // exposes /metrics
app.UseHttpMetrics();    // collects HTTP request metrics

app.Run();