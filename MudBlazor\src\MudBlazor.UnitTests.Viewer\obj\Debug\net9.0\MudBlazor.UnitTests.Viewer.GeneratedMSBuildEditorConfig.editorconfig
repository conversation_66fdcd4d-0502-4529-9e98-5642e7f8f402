is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.MudDebugAnalyzer = 
build_property.MudAllowedAttributePattern = 
build_property.MudAllowedAttributeList = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = browser
build_property.RootNamespace = MudBlazor.UnitTests
build_property.RootNamespace = MudBlazor.UnitTests
build_property.ProjectDir = C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests.Viewer\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = false
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests.Viewer
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/App.razor]
build_metadata.AdditionalFiles.TargetPath = QXBwLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = UGFnZXNcSW5kZXgucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/Shared/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = U2hhcmVkXE1haW5MYXlvdXQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Alert/AlertClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQWxlcnRcQWxlcnRDbGlja1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/AppBar/ContextualAppBarTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXBwQmFyXENvbnRleHR1YWxBcHBCYXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteAdornmentChange.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUFkb3JubWVudENoYW5nZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteAdornmentClickHandlingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUFkb3JubWVudENsaWNrSGFuZGxpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteChangeBoundObjectTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUNoYW5nZUJvdW5kT2JqZWN0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutoCompleteContainer.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9Db21wbGV0ZUNvbnRhaW5lci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteDisabledItemsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZURpc2FibGVkSXRlbXNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteFocusTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUZvY3VzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteItemTemplateDisplayTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUl0ZW1UZW1wbGF0ZURpc3BsYXlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteListBeforeAndAfterRendersWithItemsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUxpc3RCZWZvcmVBbmRBZnRlclJlbmRlcnNXaXRoSXRlbXNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteListEndRendersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUxpc3RFbmRSZW5kZXJzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteListStartRendersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZUxpc3RTdGFydFJlbmRlcnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteRequiredTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVJlcXVpcmVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteResetTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVJlc2V0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteResetValueOnEmptyText.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVJlc2V0VmFsdWVPbkVtcHR5VGV4dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteRetainFocusTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVJldGFpbkZvY3VzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteSetParametersInitialization.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVNldFBhcmFtZXRlcnNJbml0aWFsaXphdGlvbi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteStates.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVN0YXRlcy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteStrictFalseSelectedHighlight.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVN0cmljdEZhbHNlU2VsZWN0ZWRIaWdobGlnaHQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteStrictFalseTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVN0cmljdEZhbHNlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteSyncTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVN5bmNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3QxLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3QzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3Q0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3Q1LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest6.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3Q2LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest7.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3Q3LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTest8.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3Q4LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTestClearable.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3RDbGVhcmFibGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteTestCoersionAndBlur.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVRlc3RDb2Vyc2lvbkFuZEJsdXIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteValidationDataAttrTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVZhbGlkYXRpb25EYXRhQXR0clRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Autocomplete/AutocompleteValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXV0b2NvbXBsZXRlXEF1dG9jb21wbGV0ZVZhbGlkYXRpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/AvatarGroup/AvatarGroupChangeMaxTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXZhdGFyR3JvdXBcQXZhdGFyR3JvdXBDaGFuZ2VNYXhUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/AvatarGroup/AvatarGroupMaxAvatarsTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXZhdGFyR3JvdXBcQXZhdGFyR3JvdXBNYXhBdmF0YXJzVGVtcGxhdGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/AvatarGroup/AvatarGroupMaxDefaultTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXZhdGFyR3JvdXBcQXZhdGFyR3JvdXBNYXhEZWZhdWx0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/AvatarGroup/AvatarGroupRemoveTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXZhdGFyR3JvdXBcQXZhdGFyR3JvdXBSZW1vdmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/AvatarGroup/AvatarGroupTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQXZhdGFyR3JvdXBcQXZhdGFyR3JvdXBUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Badge/BadgeClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQmFkZ2VcQmFkZ2VDbGlja1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ButtonGroup/ButtonGroupWithThreeButtons.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQnV0dG9uR3JvdXBcQnV0dG9uR3JvdXBXaXRoVGhyZWVCdXR0b25zLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Button/ButtonErrorContenCaughtException.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQnV0dG9uXEJ1dHRvbkVycm9yQ29udGVuQ2F1Z2h0RXhjZXB0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Button/ButtonGroupTooltipsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQnV0dG9uXEJ1dHRvbkdyb3VwVG9vbHRpcHNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Button/ButtonSizeIconSizeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQnV0dG9uXEJ1dHRvblNpemVJY29uU2l6ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Button/ButtonsNestedDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQnV0dG9uXEJ1dHRvbnNOZXN0ZWREaXNhYmxlZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Button/IconButtonTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQnV0dG9uXEljb25CdXR0b25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Card/CardChildContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2FyZFxDYXJkQ2hpbGRDb250ZW50VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Carousel/CarouselBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2Fyb3VzZWxcQ2Fyb3VzZWxCaW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Carousel/CarouselTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2Fyb3VzZWxcQ2Fyb3VzZWxUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/BarChartSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXEJhckNoYXJ0U2VsZWN0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/BarChartWithSingleXAxisTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXEJhckNoYXJ0V2l0aFNpbmdsZVhBeGlzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/ChartsWithCustomColorsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXENoYXJ0c1dpdGhDdXN0b21Db2xvcnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/ChartsWithCustomGraphicsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXENoYXJ0c1dpdGhDdXN0b21HcmFwaGljc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/DonutChartSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXERvbnV0Q2hhcnRTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/HeatMapChartTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXEhlYXRNYXBDaGFydFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/HeatMapDynamicFontTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXEhlYXRNYXBEeW5hbWljRm9udFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/LineChartSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXExpbmVDaGFydFNlbGVjdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/LineChartWithBigValuesTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXExpbmVDaGFydFdpdGhCaWdWYWx1ZXNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/LineChartWithZeroValuesTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXExpbmVDaGFydFdpdGhaZXJvVmFsdWVzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Charts/PieChartSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hhcnRzXFBpZUNoYXJ0U2VsZWN0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/CheckBox/CheckBoxesBindAgainstArrayTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hlY2tCb3hcQ2hlY2tCb3hlc0JpbmRBZ2FpbnN0QXJyYXlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/CheckBox/CheckBoxFormTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hlY2tCb3hcQ2hlY2tCb3hGb3JtVGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/CheckBox/CheckboxLabelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hlY2tCb3hcQ2hlY2tib3hMYWJlbFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/CheckBox/CheckBoxTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hlY2tCb3hcQ2hlY2tCb3hUZXN0My5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/CheckBox/CheckBoxTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hlY2tCb3hcQ2hlY2tCb3hUZXN0NC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetChipBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0Q2hpcEJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetClearSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0Q2xlYXJTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetComparerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0Q29tcGFyZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetDefaultChipsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0RGVmYXVsdENoaXBzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetKeyboardNavigationTests.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0S2V5Ym9hcmROYXZpZ2F0aW9uVGVzdHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetLateDefaultTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0TGF0ZURlZmF1bHRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetMultiSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0TXVsdGlTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetReadOnlyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0UmVhZE9ubHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetSelectionTwoWayBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0U2VsZWN0aW9uVHdvV2F5QmluZGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetSingleSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0U2luZ2xlU2VsZWN0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ChipSet/ChipSetTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFNldFxDaGlwU2V0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Chip/ChipAvatarContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFxDaGlwQXZhdGFyQ29udGVudFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Chip/ChipOnClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ2hpcFxDaGlwT25DbGlja1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Collapse/CollapseBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ29sbGFwc2VcQ29sbGFwc2VCaW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ColorPicker/PickerWithFixedView.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ29sb3JQaWNrZXJcUGlja2VyV2l0aEZpeGVkVmlldy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ColorPicker/SimpleColorPickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcQ29sb3JQaWNrZXJcU2ltcGxlQ29sb3JQaWNrZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridAggregationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRBZ2dyZWdhdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellContextTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsQ29udGV4dFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellEditTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsRWRpdFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellEditVirtualizeServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsRWRpdFZpcnR1YWxpemVTZXJ2ZXJEYXRhVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellEditWithNullableChangeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsRWRpdFdpdGhOdWxsYWJsZUNoYW5nZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellEditWithNullableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsRWRpdFdpdGhOdWxsYWJsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellEditWithTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsRWRpdFdpdGhUZW1wbGF0ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCellTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDZWxsVGVtcGxhdGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridChildPropertiesWithSameNameSortTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDaGlsZFByb3BlcnRpZXNXaXRoU2FtZU5hbWVTb3J0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridChildRowContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDaGlsZFJvd0NvbnRlbnRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColGroupTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2xHcm91cFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColReorderRowFiltersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2xSZW9yZGVyUm93RmlsdGVyc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnChooserTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5DaG9vc2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnFilterRowPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5GaWx0ZXJSb3dQcm9wZXJ0eVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnGroupingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5Hcm91cGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnHiddenTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5IaWRkZW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnPopupCustomFilteringTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5Qb3B1cEN1c3RvbUZpbHRlcmluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnPopupFilteringTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5Qb3B1cEZpbHRlcmluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridColumnShowFilterIconsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb2x1bW5TaG93RmlsdGVySWNvbnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridConditionalLogicOnColumnsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb25kaXRpb25hbExvZ2ljT25Db2x1bW5zVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridContextMenuTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDb250ZXh0TWVudVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCultureEditableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdWx0dXJlRWRpdGFibGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCultureSimpleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdWx0dXJlU2ltcGxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCulturesTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdWx0dXJlc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCurrentPageParameterTwoWayBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdXJyZW50UGFnZVBhcmFtZXRlclR3b1dheUJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCustomFilteringTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdXN0b21GaWx0ZXJpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCustomPropertyFilterTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdXN0b21Qcm9wZXJ0eUZpbHRlclRlbXBsYXRlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridCustomSortableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRDdXN0b21Tb3J0YWJsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridDragAndDropTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWREcmFnQW5kRHJvcFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridDragAndDropWithDynamicColumnsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWREcmFnQW5kRHJvcFdpdGhEeW5hbWljQ29sdW1uc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridDynamicColumnsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWREeW5hbWljQ29sdW1uc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridEditableWithSelectColumnTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRFZGl0YWJsZVdpdGhTZWxlY3RDb2x1bW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridEditComplexPropertyExpressionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRFZGl0Q29tcGxleFByb3BlcnR5RXhwcmVzc2lvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridEditFormCustomizedDialogTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRFZGl0Rm9ybUN1c3RvbWl6ZWREaWFsb2dUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridEventCallbacksTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRFdmVudENhbGxiYWNrc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterableFalseTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJhYmxlRmFsc2VUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterableServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJhYmxlU2VydmVyRGF0YVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJhYmxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterableVirtualizeServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJhYmxlVmlydHVhbGl6ZVNlcnZlckRhdGFUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterEnumLocalizationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJFbnVtTG9jYWxpemF0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterGuid.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJHdWlkLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterPerColumnTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJQZXJDb2x1bW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterRowCustomFilteringTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJSb3dDdXN0b21GaWx0ZXJpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFilterRowHiddenTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJSb3dIaWRkZW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFiltersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaWx0ZXJzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFixedHeaderFilterTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGaXhlZEhlYWRlckZpbHRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFooterTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGb290ZXJUZW1wbGF0ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFormatTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGb3JtYXRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFormEditCloneStrategyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGb3JtRWRpdENsb25lU3RyYXRlZ3lUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFormEditTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGb3JtRWRpdFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFormFieldChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGb3JtRmllbGRDaGFuZ2VkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridFormValidationErrorsPreventUpdateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRGb3JtVmFsaWRhdGlvbkVycm9yc1ByZXZlbnRVcGRhdGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupableServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cGFibGVTZXJ2ZXJEYXRhVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupCollapseAllTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cENvbGxhcHNlQWxsVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandAllCollapseAllTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZEFsbENvbGxhcHNlQWxsVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandedAsyncTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZGVkQXN5bmNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandedFalseAsyncTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZGVkRmFsc2VBc3luY1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandedFalseServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZGVkRmFsc2VTZXJ2ZXJEYXRhVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandedFalseTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZGVkRmFsc2VUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandedServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZGVkU2VydmVyRGF0YVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupExpandedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cEV4cGFuZGVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridGroupingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRHcm91cGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridHeaderTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRIZWFkZXJUZW1wbGF0ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridHideAndResizeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRIaWRlQW5kUmVzaXplVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridHierarchyColumnTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRIaWVyYXJjaHlDb2x1bW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridIDictionaryFiltersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRJRGljdGlvbmFyeUZpbHRlcnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridInDialogFilterTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRJbkRpYWxvZ0ZpbHRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridLoadingContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRMb2FkaW5nQ29udGVudFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridLoadingProgressTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRMb2FkaW5nUHJvZ3Jlc3NUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridMultiSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRNdWx0aVNlbGVjdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridNestedNullPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWROZXN0ZWROdWxsUHJvcGVydHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridNoRecordsContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWROb1JlY29yZHNDb250ZW50VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridNoRecordsContentVirtualizeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWROb1JlY29yZHNDb250ZW50VmlydHVhbGl6ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridObservabilityTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRPYnNlcnZhYmlsaXR5VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridPaginationAllItemsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRQYWdpbmF0aW9uQWxsSXRlbXNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridPaginationNoItemsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRQYWdpbmF0aW9uTm9JdGVtc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridPaginationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRQYWdpbmF0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridPropertyColumnNullCheckTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRQcm9wZXJ0eUNvbHVtbk51bGxDaGVja1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridRedundantMenuTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRSZWR1bmRhbnRNZW51VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridRowsPerPageBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRSb3dzUGVyUGFnZUJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSelectColumnTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZWxlY3RDb2x1bW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSelectionComparerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZWxlY3Rpb25Db21wYXJlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSequenceContainsNoElementsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXF1ZW5jZUNvbnRhaW5zTm9FbGVtZW50c1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerDataColumnFilterMenuTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJEYXRhQ29sdW1uRmlsdGVyTWVudVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerDataColumnFilterRowTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJEYXRhQ29sdW1uRmlsdGVyUm93VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerDataColumnGroupingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJEYXRhQ29sdW1uR3JvdXBpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerDataWithVirtualizeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJEYXRhV2l0aFZpcnR1YWxpemVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerMultiSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJNdWx0aVNlbGVjdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerPaginationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJQYWdpbmF0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridServerSideSortableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTZXJ2ZXJTaWRlU29ydGFibGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridShowMenuIconTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTaG93TWVudUljb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSingleSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTaW5nbGVTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSortableHeaderRowTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTb3J0YWJsZUhlYWRlclJvd1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSortableTemplateColumnTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTb3J0YWJsZVRlbXBsYXRlQ29sdW1uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSortableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTb3J0YWJsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridSortableVirtualizeServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTb3J0YWJsZVZpcnR1YWxpemVTZXJ2ZXJEYXRhVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridStickyColumnsResizerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTdGlja3lDb2x1bW5zUmVzaXplclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridStickyColumnsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTdGlja3lDb2x1bW5zVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridStringContainsFilter.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRTdHJpbmdDb250YWluc0ZpbHRlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridUniqueRowKeyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRVbmlxdWVSb3dLZXlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridValidatorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRWYWxpZGF0b3JUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridVirtualizeServerDataLoadingLargeDataSetTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRWaXJ0dWFsaXplU2VydmVyRGF0YUxvYWRpbmdMYXJnZURhdGFTZXRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridVirtualizeServerDataLoadingWithCancelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRWaXJ0dWFsaXplU2VydmVyRGF0YUxvYWRpbmdXaXRoQ2FuY2VsVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridVirtualizeServerDataLoadingWithSearchGroupableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRWaXJ0dWFsaXplU2VydmVyRGF0YUxvYWRpbmdXaXRoU2VhcmNoR3JvdXBhYmxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridVirtualizeServerDataLoadingWithSearchTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRWaXJ0dWFsaXplU2VydmVyRGF0YUxvYWRpbmdXaXRoU2VhcmNoVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DataGrid/DataGridVisualStylingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0YUdyaWRcRGF0YUdyaWRWaXN1YWxTdHlsaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/AutoCloseDateRangePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxBdXRvQ2xvc2VEYXRlUmFuZ2VQaWNrZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/AutoCompleteDatePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxBdXRvQ29tcGxldGVEYXRlUGlja2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DatePickerBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUGlja2VyQmluZGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DatePickerCustomDateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUGlja2VyQ3VzdG9tRGF0ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DatePickerStaticTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUGlja2VyU3RhdGljVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DatePickerValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUGlja2VyVmFsaWRhdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateRangePickerClearableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUmFuZ2VQaWNrZXJDbGVhcmFibGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateRangePickerCloseOnClearTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUmFuZ2VQaWNrZXJDbG9zZU9uQ2xlYXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateRangePickerFormatTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUmFuZ2VQaWNrZXJGb3JtYXRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateRangePickerPresetRangeWithTimestampTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUmFuZ2VQaWNrZXJQcmVzZXRSYW5nZVdpdGhUaW1lc3RhbXBUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateRangePickerPresetWithoutTimestampTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUmFuZ2VQaWNrZXJQcmVzZXRXaXRob3V0VGltZXN0YW1wVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateRangePickerValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlUmFuZ2VQaWNrZXJWYWxpZGF0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/DateTimeMinValueDatePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxEYXRlVGltZU1pblZhbHVlRGF0ZVBpY2tlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/FixYearFixMonthTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxGaXhZZWFyRml4TW9udGhUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/PersianDatePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxQZXJzaWFuRGF0ZVBpY2tlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/SimpleMudDatePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxTaW1wbGVNdWREYXRlUGlja2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/SimpleMudMudDateRangePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxTaW1wbGVNdWRNdWREYXRlUmFuZ2VQaWNrZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/VarientDatePickerRenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxWYXJpZW50RGF0ZVBpY2tlclJlbmRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DatePicker/WrappedDatePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGF0ZVBpY2tlclxXcmFwcGVkRGF0ZVBpY2tlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/ComponentThatOpensAndClosesDialog.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXENvbXBvbmVudFRoYXRPcGVuc0FuZENsb3Nlc0RpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogOkCancel.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ09rQ2FuY2VsLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogOptionMutation.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ09wdGlvbk11dGF0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogRender.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1JlbmRlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogThatUpdatesItsTitle.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1RoYXRVcGRhdGVzSXRzVGl0bGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogToggleFullscreen.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1RvZ2dsZUZ1bGxzY3JlZW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithActionsClass.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhBY3Rpb25zQ2xhc3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithContentClass.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhDb250ZW50Q2xhc3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithEventCallback.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhFdmVudENhbGxiYWNrLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithEventCallbackTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhFdmVudENhbGxiYWNrVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithOnBackdropClickEvent.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhPbkJhY2tkcm9wQ2xpY2tFdmVudC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithParameters.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhQYXJhbWV0ZXJzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithReturnValue.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhSZXR1cm5WYWx1ZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/DialogWithTitleClass.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXERpYWxvZ1dpdGhUaXRsZUNsYXNzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/InlineDialogIsVisibleStateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXElubGluZURpYWxvZ0lzVmlzaWJsZVN0YXRlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/InlineDialogShowMethod.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXElubGluZURpYWxvZ1Nob3dNZXRob2QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/SimpleDialog.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXFNpbXBsZURpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/TestInlineDialog.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXFRlc3RJbmxpbmVEaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Dialog/TestNestedInlineDialog.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRGlhbG9nXFRlc3ROZXN0ZWRJbmxpbmVEaWFsb2cucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Drawer/DrawerContainerTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJhd2VyXERyYXdlckNvbnRhaW5lclRlc3QxLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Drawer/DrawerDialogSelectTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJhd2VyXERyYXdlckRpYWxvZ1NlbGVjdFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Drawer/DrawerNonResponsiveTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJhd2VyXERyYXdlck5vblJlc3BvbnNpdmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Drawer/DrawerResponsiveTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJhd2VyXERyYXdlclJlc3BvbnNpdmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Drawer/DrawerTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJhd2VyXERyYXdlclRlc3QxLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneBasicTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVCYXNpY1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneCanDropTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVDYW5Ecm9wVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneCustomItemSelectorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVDdXN0b21JdGVtU2VsZWN0b3JUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneDisableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVEaXNhYmxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneDraggingTestCantDropSecondZoneTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVEcmFnZ2luZ1Rlc3RDYW50RHJvcFNlY29uZFpvbmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneDynamicItemCollectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVEeW5hbWljSXRlbUNvbGxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneItemClassSelectorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVJdGVtQ2xhc3NTZWxlY3RvclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneItemOnItemPickedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVJdGVtT25JdGVtUGlja2VkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneReorderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVSZW9yZGVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/DropZone/DropzoneVisbilityTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRHJvcFpvbmVcRHJvcHpvbmVWaXNiaWxpdHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Element/ElementReferenceExceptionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRWxlbWVudFxFbGVtZW50UmVmZXJlbmNlRXhjZXB0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Element/ElementTestEventNull.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRWxlbWVudFxFbGVtZW50VGVzdEV2ZW50TnVsbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ExpansionPanel/ExpansionPanelExpandedMultipleWithoutMultipleExpansionSetTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRXhwYW5zaW9uUGFuZWxcRXhwYW5zaW9uUGFuZWxFeHBhbmRlZE11bHRpcGxlV2l0aG91dE11bHRpcGxlRXhwYW5zaW9uU2V0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ExpansionPanel/ExpansionPanelExpansionsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRXhwYW5zaW9uUGFuZWxcRXhwYW5zaW9uUGFuZWxFeHBhbnNpb25zVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ExpansionPanel/ExpansionPanelMultiExpansionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRXhwYW5zaW9uUGFuZWxcRXhwYW5zaW9uUGFuZWxNdWx0aUV4cGFuc2lvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ExpansionPanel/ExpansionPanelStartExpandedMultipleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRXhwYW5zaW9uUGFuZWxcRXhwYW5zaW9uUGFuZWxTdGFydEV4cGFuZGVkTXVsdGlwbGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ExpansionPanel/ExpansionPanelStartExpandedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRXhwYW5zaW9uUGFuZWxcRXhwYW5zaW9uUGFuZWxTdGFydEV4cGFuZGVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ExpansionPanel/ExpansionPanelTwoWayBIndingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRXhwYW5zaW9uUGFuZWxcRXhwYW5zaW9uUGFuZWxUd29XYXlCSW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Field/FieldTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmllbGRcRmllbGRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadAppendMultipleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkQXBwZW5kTXVsdGlwbGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadChangeCountTests.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkQ2hhbmdlQ291bnRUZXN0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkRGlzYWJsZWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadFormValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkRm9ybVZhbGlkYXRpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadMultipleFilesTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkTXVsdGlwbGVGaWxlc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadOnFilesChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkT25GaWxlc0NoYW5nZWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/FileUpload/FileUploadWithDragAndDropActivatorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRmlsZVVwbG9hZFxGaWxlVXBsb2FkV2l0aERyYWdBbmREcm9wQWN0aXZhdG9yVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/EditFormIssue1229.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxFZGl0Rm9ybUlzc3VlMTIyOS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/EditFormOnFieldChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxFZGl0Rm9ybU9uRmllbGRDaGFuZ2VkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FieldValidationWithoutRequiredFormTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGaWVsZFZhbGlkYXRpb25XaXRob3V0UmVxdWlyZWRGb3JtVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormAsyncValidationWithFieldChangedSubscriberTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtQXN5bmNWYWxpZGF0aW9uV2l0aEZpZWxkQ2hhbmdlZFN1YnNjcmliZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormAutomaticValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtQXV0b21hdGljVmFsaWRhdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormComponentUpdateValidationMessagesOnEditContextChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtQ29tcG9uZW50VXBkYXRlVmFsaWRhdGlvbk1lc3NhZ2VzT25FZGl0Q29udGV4dENoYW5nZWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormConversionErrorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtQ29udmVyc2lvbkVycm9yVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormFieldChangedPickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtRmllbGRDaGFuZ2VkUGlja2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormFieldChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtRmllbGRDaGFuZ2VkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormIsTouchedNestedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtSXNUb3VjaGVkTmVzdGVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormIsTouchedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtSXNUb3VjaGVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormIsValidTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtSXNWYWxpZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormIsValidTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtSXNWYWxpZFRlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormIsValidTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtSXNWYWxpZFRlc3QzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormIsValidTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtSXNWYWxpZFRlc3Q0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormNestedReadOnlyDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtTmVzdGVkUmVhZE9ubHlEaXNhYmxlZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormReadOnlyDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtUmVhZE9ubHlEaXNhYmxlZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormResetTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtUmVzZXRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormShouldRegisterOnlyTopSubscribeToParentFormFormControlsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtU2hvdWxkUmVnaXN0ZXJPbmx5VG9wU3Vic2NyaWJlVG9QYXJlbnRGb3JtRm9ybUNvbnRyb2xzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormValidationOverrideFieldValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtVmFsaWRhdGlvbk92ZXJyaWRlRmllbGRWYWxpZGF0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormValidationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtVmFsaWRhdGlvblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormValidationTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtVmFsaWRhdGlvblRlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormValidationTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtVmFsaWRhdGlvblRlc3QzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormValidationTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtVmFsaWRhdGlvblRlc3Q0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormValidationTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtVmFsaWRhdGlvblRlc3Q1LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithCheckBoxAndTextFieldsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aENoZWNrQm94QW5kVGV4dEZpZWxkc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithCheckBoxTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aENoZWNrQm94VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithChildForm.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aENoaWxkRm9ybS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithColorPickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aENvbG9yUGlja2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithDatePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aERhdGVQaWNrZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithDateRangePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aERhdGVSYW5nZVBpY2tlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithFileUploadAndDragAndDropActivatorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aEZpbGVVcGxvYWRBbmREcmFnQW5kRHJvcEFjdGl2YXRvclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithFileUploadTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aEZpbGVVcGxvYWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithRadioGroupTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aFJhZGlvR3JvdXBUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithSingleTextField.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aFNpbmdsZVRleHRGaWVsZC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/FormWithTimePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxGb3JtV2l0aFRpbWVQaWNrZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Form/MudFormTestable.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcRm9ybVxNdWRGb3JtVGVzdGFibGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/General/AllInputsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcR2VuZXJhbFxBbGxJbnB1dHNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Hidden/BreakpointProviderWithMudHiddenTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcSGlkZGVuXEJyZWFrcG9pbnRQcm92aWRlcldpdGhNdWRIaWRkZW5UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Hidden/RenderMultipleHiddenInParallel.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcSGlkZGVuXFJlbmRlck11bHRpcGxlSGlkZGVuSW5QYXJhbGxlbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Hidden/SimpleMudHiddenTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcSGlkZGVuXFNpbXBsZU11ZEhpZGRlblRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Link/LinkErrorContenCaughtException.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlua1xMaW5rRXJyb3JDb250ZW5DYXVnaHRFeGNlcHRpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListDenseInheritanceTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0RGVuc2VJbmhlcml0YW5jZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListItemGuttersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0SXRlbUd1dHRlcnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListItemRippleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0SXRlbVJpcHBsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListItemTabIndexTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0SXRlbVRhYkluZGV4VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListMultiSelectionBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0TXVsdGlTZWxlY3Rpb25CaW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListMultiSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0TXVsdGlTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListSelectionInitialValueTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0U2VsZWN0aW9uSW5pdGlhbFZhbHVlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/List/ListSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTGlzdFxMaXN0U2VsZWN0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Logger/LoggerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTG9nZ2VyXExvZ2dlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Mask/DifferentMaskImplementationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWFza1xEaWZmZXJlbnRNYXNrSW1wbGVtZW50YXRpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Mask/FormResetMaskTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWFza1xGb3JtUmVzZXRNYXNrVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Mask/MaskedTextFieldTwoWayBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWFza1xNYXNrZWRUZXh0RmllbGRUd29XYXlCaW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Mask/MaskTwoWayBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWFza1xNYXNrVHdvV2F5QmluZGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Mask/ReadonlyMaskedTextFieldTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWFza1xSZWFkb25seU1hc2tlZFRleHRGaWVsZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/ContextMenuTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxDb250ZXh0TWVudVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuAccessibilityTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51QWNjZXNzaWJpbGl0eVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuActivatorsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51QWN0aXZhdG9yc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuErrorContenCaughtException.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51RXJyb3JDb250ZW5DYXVnaHRFeGNlcHRpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuHrefTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51SHJlZlRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuIsOpenChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51SXNPcGVuQ2hhbmdlZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuItemIconTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51SXRlbUljb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuItemLabelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51SXRlbUxhYmVsVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuNoFlipScrollBarTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51Tm9GbGlwU2Nyb2xsQmFyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuPositionAtCursorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51UG9zaXRpb25BdEN1cnNvclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51VGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuTestDisabledCustomActivator.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51VGVzdERpc2FibGVkQ3VzdG9tQWN0aXZhdG9yLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuTestMouseOver.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51VGVzdE1vdXNlT3Zlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuTestVariants.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51VGVzdFZhcmlhbnRzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuTwoWayTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51VHdvV2F5VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Menu/MenuWithNestingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTWVudVxNZW51V2l0aE5lc3RpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Navigation/NavigationAccessibilityTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2aWdhdGlvblxOYXZpZ2F0aW9uQWNjZXNzaWJpbGl0eVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NavLink/NavLinkDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2TGlua1xOYXZMaW5rRGlzYWJsZWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NavLink/NavLinkErrorContenCaughtException.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2TGlua1xOYXZMaW5rRXJyb3JDb250ZW5DYXVnaHRFeGNlcHRpb24ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NavMenu/NavGroupWithExpandedBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2TWVudVxOYXZHcm91cFdpdGhFeHBhbmRlZEJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NavMenu/NavMenuGroupDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2TWVudVxOYXZNZW51R3JvdXBEaXNhYmxlZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NavMenu/NavMenuOneWay.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2TWVudVxOYXZNZW51T25lV2F5LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NavMenu/NavMenuTwoWay.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTmF2TWVudVxOYXZNZW51VHdvV2F5LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NumericField/DebouncedNumericFieldCultureChangeRerenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTnVtZXJpY0ZpZWxkXERlYm91bmNlZE51bWVyaWNGaWVsZEN1bHR1cmVDaGFuZ2VSZXJlbmRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NumericField/DebouncedNumericFieldRerenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTnVtZXJpY0ZpZWxkXERlYm91bmNlZE51bWVyaWNGaWVsZFJlcmVuZGVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NumericField/NumericFieldCultureTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTnVtZXJpY0ZpZWxkXE51bWVyaWNGaWVsZEN1bHR1cmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NumericField/NumericFieldRenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTnVtZXJpY0ZpZWxkXE51bWVyaWNGaWVsZFJlbmRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NumericField/NumericFieldRequiredTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTnVtZXJpY0ZpZWxkXE51bWVyaWNGaWVsZFJlcXVpcmVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/NumericField/NumericFieldTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcTnVtZXJpY0ZpZWxkXE51bWVyaWNGaWVsZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Overlay/OverlayDialogTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcT3ZlcmxheVxPdmVybGF5RGlhbG9nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Overlay/OverlayNestedFreezeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcT3ZlcmxheVxPdmVybGF5TmVzdGVkRnJlZXplVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Overlay/OverlayTestDialog.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcT3ZlcmxheVxPdmVybGF5VGVzdERpYWxvZy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Overlay/OverlayVisibleBindingWithAutoCloseTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcT3ZlcmxheVxPdmVybGF5VmlzaWJsZUJpbmRpbmdXaXRoQXV0b0Nsb3NlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Pagination/PaginationButtonTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUGFnaW5hdGlvblxQYWdpbmF0aW9uQnV0dG9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Pagination/PaginationCountTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUGFnaW5hdGlvblxQYWdpbmF0aW9uQ291bnRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Pagination/PaginationHidePageButtonsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUGFnaW5hdGlvblxQYWdpbmF0aW9uSGlkZVBhZ2VCdXR0b25zVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Pagination/PaginationStylesTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUGFnaW5hdGlvblxQYWdpbmF0aW9uU3R5bGVzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Picker/SimplePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUGlja2VyXFNpbXBsZVBpY2tlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverAppBarMenuSubMenuTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyQXBwQmFyTWVudVN1Yk1lbnVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverComplexContent.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyQ29tcGxleENvbnRlbnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverDataGridFilterOptionsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyRGF0YUdyaWRGaWx0ZXJPcHRpb25zVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverDirectionAndLocationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyRGlyZWN0aW9uQW5kTG9jYXRpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverDrawerWithSelectTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyRHJhd2VyV2l0aFNlbGVjdFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverDuplicationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyRHVwbGljYXRpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverOverlaySelectTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyT3ZlcmxheVNlbGVjdFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyUHJvcGVydHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverProviderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyUHJvdmlkZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Popover/PopoverTooltipInOverlayTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUG9wb3ZlclxQb3BvdmVyVG9vbHRpcEluT3ZlcmxheVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupExceptionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwRXhjZXB0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupRequiredTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwUmVxdWlyZWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest6.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDYucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioGroupTest7.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb0dyb3VwVGVzdDcucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/RadioGroup/RadioReadOnlyDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcUmFkaW9Hcm91cFxSYWRpb1JlYWRPbmx5RGlzYWJsZWRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Scroll/ScrollToTopTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2Nyb2xsXFNjcm9sbFRvVG9wVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectCustomizedTextTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0Q3VzdG9taXplZFRleHRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest6.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDYucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTest7.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdDcucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectTestRequiredValue.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0VGVzdFJlcXVpcmVkVmFsdWUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectWithCustomComparerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0V2l0aEN1c3RvbUNvbXBhcmVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectWithInitialValues.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0V2l0aEluaXRpYWxWYWx1ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/MultiSelectWithValueContainZeroTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XE11bHRpU2VsZWN0V2l0aFZhbHVlQ29udGFpblplcm9UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/ReloadSelectItemsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFJlbG9hZFNlbGVjdEl0ZW1zVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/ReselectValueTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFJlc2VsZWN0VmFsdWVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectClearableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdENsZWFyYWJsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectEventCountTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdEV2ZW50Q291bnRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectFocusAndTypeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdEZvY3VzQW5kVHlwZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectPopoverRelativeWidthTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFBvcG92ZXJSZWxhdGl2ZVdpZHRoVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectRequiredTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFJlcXVpcmVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectScrollDrawerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFNjcm9sbERyYXdlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFRlc3QxLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFRlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectUnrepresentableValueTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFVucmVwcmVzZW50YWJsZVZhbHVlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectUnrepresentableValueTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFVucmVwcmVzZW50YWJsZVZhbHVlVGVzdDIucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectValidationDataAttrTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFZhbGlkYXRpb25EYXRhQXR0clRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectVariantsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFZhcmlhbnRzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectWithEnumTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFdpdGhFbnVtVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Select/SelectWithoutItemPresentersTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2VsZWN0XFNlbGVjdFdpdGhvdXRJdGVtUHJlc2VudGVyc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Slider/SliderWithContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2xpZGVyXFNsaWRlcldpdGhDb250ZW50VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Slider/SliderWithCustomValueLabelContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2xpZGVyXFNsaWRlcldpdGhDdXN0b21WYWx1ZUxhYmVsQ29udGVudFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Slider/SliderWithNullable.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2xpZGVyXFNsaWRlcldpdGhOdWxsYWJsZS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Slider/SliderWithTwoBindValues.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU2xpZGVyXFNsaWRlcldpdGhUd29CaW5kVmFsdWVzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Snackbar/SnackbarCustomActionOnCloseTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU25hY2tiYXJcU25hY2tiYXJDdXN0b21BY3Rpb25PbkNsb3NlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Snackbar/SnackbarCustomComponent.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU25hY2tiYXJcU25hY2tiYXJDdXN0b21Db21wb25lbnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Snackbar/SnackbarCustomComponentMessageTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU25hY2tiYXJcU25hY2tiYXJDdXN0b21Db21wb25lbnRNZXNzYWdlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Snackbar/SnackbarIconConfiguationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU25hY2tiYXJcU25hY2tiYXJJY29uQ29uZmlndWF0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Snackbar/SnackbarLongMessageTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU25hY2tiYXJcU25hY2tiYXJMb25nTWVzc2FnZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Snackbar/SnackbarRenderFragmentMessageTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU25hY2tiYXJcU25hY2tiYXJSZW5kZXJGcmFnbWVudE1lc3NhZ2VUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Stack/BreakpointReverseRowTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3RhY2tcQnJlYWtwb2ludFJldmVyc2VSb3dUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Stack/BreakpointReverseTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3RhY2tcQnJlYWtwb2ludFJldmVyc2VUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Stack/BreakpointRowTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3RhY2tcQnJlYWtwb2ludFJvd1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Stack/BreakpointTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3RhY2tcQnJlYWtwb2ludFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Stepper/StepperTwoWayBindingTestComponent.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3RlcHBlclxTdGVwcGVyVHdvV2F5QmluZGluZ1Rlc3RDb21wb25lbnQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/SwipeArea/SwipeAreaOnSwipeEndTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3dpcGVBcmVhXFN3aXBlQXJlYU9uU3dpcGVFbmRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/SwipeArea/SwipeAreaTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3dpcGVBcmVhXFN3aXBlQXJlYVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Switch/MudSwitchTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3dpdGNoXE11ZFN3aXRjaFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Switch/SwitchWithLabelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcU3dpdGNoXFN3aXRjaFdpdGhMYWJlbFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableComparerContextTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVDb21wYXJlckNvbnRleHRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableCurrentPageParameterTwoWayBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVDdXJyZW50UGFnZVBhcmFtZXRlclR3b1dheUJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableCustomEditButtonItemContextRenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVDdXN0b21FZGl0QnV0dG9uSXRlbUNvbnRleHRSZW5kZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableCustomEditButtonRenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVDdXN0b21FZGl0QnV0dG9uUmVuZGVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableDisabledSortTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVEaXNhYmxlZFNvcnRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableEditButtonRenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVFZGl0QnV0dG9uUmVuZGVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableFilterTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVGaWx0ZXJUZXN0MS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableGroupingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVHcm91cGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableGroupingTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVHcm91cGluZ1Rlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableGroupLoadingAndNoRecordsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVHcm91cExvYWRpbmdBbmROb1JlY29yZHNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableInlineEditCancelNoSelectedItemTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVJbmxpbmVFZGl0Q2FuY2VsTm9TZWxlY3RlZEl0ZW1UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableInlineEditCancelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVJbmxpbmVFZGl0Q2FuY2VsVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableInlineEditRowBlockingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVJbmxpbmVFZGl0Um93QmxvY2tpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableInlineEditSortTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVJbmxpbmVFZGl0U29ydFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableInlineEditTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVJbmxpbmVFZGl0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableInlineEditTestApplyButtons.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVJbmxpbmVFZGl0VGVzdEFwcGx5QnV0dG9ucy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableLoadingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVMb2FkaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionCheckboxExecutesCallback.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvbkNoZWNrYm94RXhlY3V0ZXNDYWxsYmFjay5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionItemsTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvbkl0ZW1zVGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionSelectedItemsChangedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblNlbGVjdGVkSXRlbXNDaGFuZ2VkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionServerDataTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblNlcnZlckRhdGFUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3QxLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest2B.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3QyQi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3QzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q1LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest6.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q2LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest6B.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q2Qi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest7.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q3LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest8.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q4LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionTest9.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblRlc3Q5LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelectionVirtualizedTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvblZpcnR1YWxpemVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelection_CheckboxAndRowClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvbl9DaGVja2JveEFuZFJvd0NsaWNrVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelection_IgnoreCheckbox_RowClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvbl9JZ25vcmVDaGVja2JveF9Sb3dDbGlja1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableMultiSelection_MultiGrouping_DefaultCheckboxStatesTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVNdWx0aVNlbGVjdGlvbl9NdWx0aUdyb3VwaW5nX0RlZmF1bHRDaGVja2JveFN0YXRlc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableNotEditableRowTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVOb3RFZGl0YWJsZVJvd1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TablePagerChangeRowsPerPageTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVQYWdlckNoYW5nZVJvd3NQZXJQYWdlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TablePagerInfoTextTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVQYWdlckluZm9UZXh0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TablePageSizeOptionsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVQYWdlU2l6ZU9wdGlvbnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TablePaginationTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVQYWdpbmF0aW9uVGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TablePagingTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVQYWdpbmdUZXN0MS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableRecordComparerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVSZWNvcmRDb21wYXJlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableRowClassStyleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVSb3dDbGFzc1N0eWxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableRowClickNotEditableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVSb3dDbGlja05vdEVkaXRhYmxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableRowClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVSb3dDbGlja1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableRowHoverTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVSb3dIb3ZlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableRowsPerPageTwoWayBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVSb3dzUGVyUGFnZVR3b1dheUJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerDataLoadingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJEYXRhTG9hZGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerDataLoadingTestWithCancel.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJEYXRhTG9hZGluZ1Rlc3RXaXRoQ2FuY2VsLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3QxLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3QyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3QzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3Q0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest4b.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3Q0Yi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3Q1LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest6.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3Q2LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableServerSideDataTest7.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTZXJ2ZXJTaWRlRGF0YVRlc3Q3LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableSingleSelectionTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTaW5nbGVTZWxlY3Rpb25UZXN0MS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableSortLabelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTb3J0TGFiZWxUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Table/TableStickyVisualTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFibGVcVGFibGVTdGlja3lWaXN1YWxUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/ActivateDisabledTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xBY3RpdmF0ZURpc2FibGVkVGFic1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/CancelActivationTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xDYW5jZWxBY3RpdmF0aW9uVGFic1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/DynamicTabsSimpleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xEeW5hbWljVGFic1NpbXBsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/DynamicTabsWithKeyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xEeW5hbWljVGFic1dpdGhLZXlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/HtmlTextTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xIdG1sVGV4dFRhYnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/KeepTabsAlive/TabsKeepAlivePanel.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xLZWVwVGFic0FsaXZlXFRhYnNLZWVwQWxpdmVQYW5lbC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/KeepTabsAlive/TabsKeepAliveTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xLZWVwVGFic0FsaXZlXFRhYnNLZWVwQWxpdmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/MinimumWidthTabs.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xNaW5pbXVtV2lkdGhUYWJzLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/ScrollableTabsRenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTY3JvbGxhYmxlVGFic1JlbmRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/ScrollableTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTY3JvbGxhYmxlVGFic1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/SelectedIndexTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTZWxlY3RlZEluZGV4VGFic1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/SimpleDynamicTabsInteractionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTaW1wbGVEeW5hbWljVGFic0ludGVyYWN0aW9uVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/SimpleDynamicTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTaW1wbGVEeW5hbWljVGFic1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/SimpleDynamicTabsTestWithToolTips.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTaW1wbGVEeW5hbWljVGFic1Rlc3RXaXRoVG9vbFRpcHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/SimplifiedScrollableTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xTaW1wbGlmaWVkU2Nyb2xsYWJsZVRhYnNUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabPanelIconColorTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJQYW5lbEljb25Db2xvclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabsAddingRemovingTabsTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJzQWRkaW5nUmVtb3ZpbmdUYWJzVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabsRippleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJzUmlwcGxlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabsVisibleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJzVmlzaWJsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabsWithHeaderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJzV2l0aEhlYWRlclRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabsWithMenuInHeader.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJzV2l0aE1lbnVJbkhlYWRlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/TabsWithPrePanelContent.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUYWJzV2l0aFByZVBhbmVsQ29udGVudC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tabs/ToggleTabsSlideAnimationTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGFic1xUb2dnbGVUYWJzU2xpZGVBbmltYXRpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/DebouncedTextFieldFormatChangeRerenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXERlYm91bmNlZFRleHRGaWVsZEZvcm1hdENoYW5nZVJlcmVuZGVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/DebouncedTextFieldRerenderTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXERlYm91bmNlZFRleHRGaWVsZFJlcmVuZGVyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/DebouncedTextFieldTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXERlYm91bmNlZFRleHRGaWVsZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/MultilineTextfieldBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXE11bHRpbGluZVRleHRmaWVsZEJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/OutlineLabelBackgroundTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXE91dGxpbmVMYWJlbEJhY2tncm91bmRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldAutoGrowTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZEF1dG9Hcm93VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldClearableTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZENsZWFyYWJsZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldErrorContenCaughtException.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZEVycm9yQ29udGVuQ2F1Z2h0RXhjZXB0aW9uLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldNestedInFieldTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZE5lc3RlZEluRmllbGRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldRequiredTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZFJlcXVpcmVkVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldShrinkLabelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZFNocmlua0xhYmVsVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldUpdateViaBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZFVwZGF0ZVZpYUJpbmRpbmdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldValidationDataAttrTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZFZhbGlkYXRpb25EYXRhQXR0clRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TextField/TextFieldWithLongLabelTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGV4dEZpZWxkXFRleHRGaWVsZFdpdGhMb25nTGFiZWxUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ThemeProvider/ThemeProviderObserveSystemThemeChangeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGhlbWVQcm92aWRlclxUaGVtZVByb3ZpZGVyT2JzZXJ2ZVN5c3RlbVRoZW1lQ2hhbmdlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Timeline/HorizontalTimelineInsideVerticalTimelineTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGltZWxpbmVcSG9yaXpvbnRhbFRpbWVsaW5lSW5zaWRlVmVydGljYWxUaW1lbGluZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Timeline/TimelineTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGltZWxpbmVcVGltZWxpbmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TimePicker/AutoCompleteTimePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGltZVBpY2tlclxBdXRvQ29tcGxldGVUaW1lUGlja2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TimePicker/SimpleTimePickerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVGltZVBpY2tlclxTaW1wbGVUaW1lUGlja2VyVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupBindMultiSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBCaW5kTXVsdGlTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupBindTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBCaW5kVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupCustomFragmentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBDdXN0b21GcmFnbWVudFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupDisabledTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBEaXNhYmxlZFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupInitializeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBJbml0aWFsaXplVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupRemoveTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBSZW1vdmVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupToggleSelectionTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBUb2dnbGVTZWxlY3Rpb25UZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleGroup/ToggleGroupValueChangedSameValueTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlR3JvdXBcVG9nZ2xlR3JvdXBWYWx1ZUNoYW5nZWRTYW1lVmFsdWVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToggleIconButton/ToggleIconButtonTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9nZ2xlSWNvbkJ1dHRvblxUb2dnbGVJY29uQnV0dG9uVGVzdDEucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/ToolBar/ToolBarWrapContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbEJhclxUb29sQmFyV3JhcENvbnRlbnRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipClickTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwQ2xpY2tUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/ToolTipContainerPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sVGlwQ29udGFpbmVyUHJvcGVydHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipDisabledPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwRGlzYWJsZWRQcm9wZXJ0eVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipFlexScenariosTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwRmxleFNjZW5hcmlvc1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipInlineTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwSW5saW5lVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/ToolTipPlacementPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sVGlwUGxhY2VtZW50UHJvcGVydHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/ToolTipPopoverClassPropertyTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sVGlwUG9wb3ZlckNsYXNzUHJvcGVydHlUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipStylingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwU3R5bGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipVisiblePropTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwVmlzaWJsZVByb3BUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipWithRenderFragmentContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwV2l0aFJlbmRlckZyYWdtZW50Q29udGVudFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Tooltip/TooltipWithTextTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVG9vbHRpcFxUb29sdGlwV2l0aFRleHRUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/DisabledTreeViewTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcRGlzYWJsZWRUcmVlVmlld1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/ItemSelectableTreeViewTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcSXRlbVNlbGVjdGFibGVUcmVlVmlld1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/ItemVisibleTreeViewTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcSXRlbVZpc2libGVUcmVlVmlld1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/SimpleTreeViewTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcU2ltcGxlVHJlZVZpZXdUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewAutoExpandTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdBdXRvRXhwYW5kVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewAutoSelectParentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdBdXRvU2VsZWN0UGFyZW50VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewComparerMultiSelectTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdDb21wYXJlck11bHRpU2VsZWN0VGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewCompareTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdDb21wYXJlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewFilterFuncTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdGaWx0ZXJGdW5jVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewItemBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdJdGVtQmluZGluZ1Rlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewItemSelectedBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdJdGVtU2VsZWN0ZWRCaW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewRippleTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdSaXBwbGVUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewSelectionBindingTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdTZWxlY3Rpb25CaW5kaW5nVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewServerTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdTZXJ2ZXJUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewServerTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdTZXJ2ZXJUZXN0Mi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTemplateTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZW1wbGF0ZVRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0MS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0Mi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest3.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0My5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest4.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0NC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest5.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0NS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest6.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0Ni5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest7.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0Ny5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/TreeView/TreeViewTest8.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVHJlZVZpZXdcVHJlZVZpZXdUZXN0OC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/EventUtil1Test.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXEV2ZW50VXRpbDFUZXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateChildBindingTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlQ2hpbGRCaW5kaW5nVGVzdENvbXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateChildComp1.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlQ2hpbGRDb21wMS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateChildComp2.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlQ2hpbGRDb21wMi5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateChildParentTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlQ2hpbGRQYXJlbnRUZXN0Q29tcC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateComparerStaticTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlQ29tcGFyZXJTdGF0aWNUZXN0Q29tcC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateComparerSwapTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlQ29tcGFyZXJTd2FwVGVzdENvbXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateEventArgsTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlRXZlbnRBcmdzVGVzdENvbXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateMultipleScopeTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlTXVsdGlwbGVTY29wZVRlc3RDb21wLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateParentBindingTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlUGFyZW50QmluZGluZ1Rlc3RDb21wLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateSharedHandlerTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlU2hhcmVkSGFuZGxlclRlc3RDb21wLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateSharedInheritanceHandlerTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlU2hhcmVkSW5oZXJpdGFuY2VIYW5kbGVyVGVzdENvbXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/ParameterStateTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFBhcmFtZXRlclN0YXRlVGVzdENvbXAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/SharedStateHandlerTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFNoYXJlZFN0YXRlSGFuZGxlclRlc3RDb21wLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Utilities/SharedStateInheritanceTestComp.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVXRpbGl0aWVzXFNoYXJlZFN0YXRlSW5oZXJpdGFuY2VUZXN0Q29tcC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Virtualize/VirtualizeNoRecordsContentTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVmlydHVhbGl6ZVxWaXJ0dWFsaXplTm9SZWNvcmRzQ29udGVudFRlc3QucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/TestComponents/Virtualize/VirtualizeTest.razor]
build_metadata.AdditionalFiles.TargetPath = VGVzdENvbXBvbmVudHNcVmlydHVhbGl6ZVxWaXJ0dWFsaXplVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.UnitTests.Viewer/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 
