﻿@namespace MudBlazor.Charts
@inherits MudChartBase

@if (MudChartParent?.ChartOptions.ShowLegend ?? true)
{
    <div @attributes="UserAttributes" class="mud-chart-legend">
        @foreach (var item in Data)        
        {
            <div class="mud-chart-legend-item" @onclick=@(()=>{ if (MudChartParent!=null) { MudChartParent.SelectedIndex=item.Index; }}) @onclick:stopPropagation=@(MudChartParent!=null) >
                @if(MudChartParent?.CanHideSeries == false)
                {
                    <span class="mud-chart-legend-marker" style="@($"background-color:{MudChartParent.ChartOptions.ChartPalette.GetValue(item.Index % MudChartParent.ChartOptions.ChartPalette.Length)}")"></span>
                    <MudText Typo="Typo.body2" HtmlTag="span">@item.Labels</MudText>
                }
                else
                {   
                    <div class="mud-chart-legend-checkbox" style="@GetCheckBoxStyle(item.Index)">    
                        <MudCheckBox Value="@item.Visible" ValueChanged="@((bool value) => item.HandleCheckboxChangeAsync())"></MudCheckBox>
                        <MudText Typo="Typo.body2" Class="ml-1" HtmlTag="span">@item.Labels</MudText>
                    </div> 
                }                 
            </div>
        }
    </div>
}
