{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"MudBlazor/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.12", "Microsoft.AspNetCore.Components.Web": "8.0.12", "Microsoft.CodeAnalysis.ResxSourceGenerator": "3.11.0-beta1.24605.2", "Microsoft.Extensions.Localization": "8.0.12", "Microsoft.NET.ILLink.Tasks": "8.0.19", "MudBlazor.JSCompiler": "1.0.17", "MudBlazor.SassCompiler": "2.0.7"}, "runtime": {"MudBlazor.dll": {}}}, "Microsoft.AspNetCore.Authorization/8.0.12": {"dependencies": {"Microsoft.AspNetCore.Metadata": "8.0.12", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.AspNetCore.Components/8.0.12": {"dependencies": {"Microsoft.AspNetCore.Authorization": "8.0.12", "Microsoft.AspNetCore.Components.Analyzers": "8.0.12"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.AspNetCore.Components.Analyzers/8.0.12": {}, "Microsoft.AspNetCore.Components.Forms/8.0.12": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.12"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.AspNetCore.Components.Web/8.0.12": {"dependencies": {"Microsoft.AspNetCore.Components": "8.0.12", "Microsoft.AspNetCore.Components.Forms": "8.0.12", "Microsoft.Extensions.DependencyInjection": "8.0.1", "Microsoft.Extensions.Primitives": "8.0.0", "Microsoft.JSInterop": "8.0.12", "System.IO.Pipelines": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.AspNetCore.Metadata/8.0.12": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.CodeAnalysis.ResxSourceGenerator/3.11.0-beta1.24605.2": {}, "Microsoft.Extensions.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.2"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Localization/8.0.12": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Localization.Abstractions": "8.0.12", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.Extensions.Localization.Abstractions/8.0.12": {"runtime": {"lib/net8.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.Options/8.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.JSInterop/8.0.12": {"runtime": {"lib/net8.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1224.60312"}}}, "Microsoft.NET.ILLink.Tasks/8.0.19": {}, "MudBlazor.JSCompiler/1.0.17": {"runtime": {"lib/netstandard2.0/MudBlazor.JSCompiler.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MudBlazor.SassCompiler/2.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Hosting.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "8.0.2"}, "runtime": {"lib/net6.0/AspNetCore.SassCompiler.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.IO.Pipelines/8.0.0": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}}}, "libraries": {"MudBlazor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-x3E6p69tEktHakgch0cR771EGvJmxRz86HD3YAg0Mag8IimC/OcZtPBwp48zLJvnxj3W4ZELSQ+gZStgClzuRg==", "path": "microsoft.aspnetcore.authorization/8.0.12", "hashPath": "microsoft.aspnetcore.authorization.8.0.12.nupkg.sha512"}, "Microsoft.AspNetCore.Components/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-oNKQXyuEpfGGcG8bRbl4kKzkTENkAWTZWmT7W69j2srrpbruM9JCguxBZeOqT1ER48hSEdxuMB0EtR0sL/6EKA==", "path": "microsoft.aspnetcore.components/8.0.12", "hashPath": "microsoft.aspnetcore.components.8.0.12.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-pZVRMJrtscHDxZE9JvdlIWin5zPHhbCod/J6g7hP4tyLyxFI7w+2XT9KRR9oo5pSGBHOWl9SHvSoRTh6UVihBg==", "path": "microsoft.aspnetcore.components.analyzers/8.0.12", "hashPath": "microsoft.aspnetcore.components.analyzers.8.0.12.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-2AuVSp77xo3N6wvmAdP7bhxXdy5nKJiypCVEfSXqtd+oNQZxdZ/dKtbs8bYtDwSXpBN/fukaKt/3WjME1J9pDw==", "path": "microsoft.aspnetcore.components.forms/8.0.12", "hashPath": "microsoft.aspnetcore.components.forms.8.0.12.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-7dw3N6kH9FNUmJIQgvDtwcVCEdrN0GBwv1Dsej2B+GoaTRtCLuA1tkPm0Vyk+LpLHa0p9CTOUYvRJa07+ksXAw==", "path": "microsoft.aspnetcore.components.web/8.0.12", "hashPath": "microsoft.aspnetcore.components.web.8.0.12.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-TVB3Kcm8CBvFJ3roOCJ4XcTCiY/G2pUjKIm6M1QyslJYlJGmL6WFdr6o8OsT4jdqDnB2Heu+MQtQsyWMpTaAlw==", "path": "microsoft.aspnetcore.metadata/8.0.12", "hashPath": "microsoft.aspnetcore.metadata.8.0.12.nupkg.sha512"}, "Microsoft.CodeAnalysis.ResxSourceGenerator/3.11.0-beta1.24605.2": {"type": "package", "serviceable": true, "sha512": "sha512-8knaIsNJLuapK1LDTVISNBiU0uKLsVLU/ooCC7uPieEyrv61txxtb0pIfZy7+oKhwnpK8oFaEF10ftBRBxcCig==", "path": "microsoft.codeanalysis.resxsourcegenerator/3.11.0-beta1.24605.2", "hashPath": "microsoft.codeanalysis.resxsourcegenerator.3.11.0-beta1.24605.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lu41BWNmwhKr6LgyQvcYBOge0pPvmiaK8R5UHXX4//wBhonJyWcT2OK1mqYfEM5G7pTf31fPrpIHOT6sN7EGOA==", "path": "microsoft.extensions.configuration/3.1.0", "hashPath": "microsoft.extensions.configuration.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESz6bVoDQX7sgWdKHF6G9Pq672T8k+19AFb/txDXwdz7MoqaNQj2/in3agm/3qae9V+WvQZH86LLTNVo0it8vQ==", "path": "microsoft.extensions.configuration.abstractions/3.1.0", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9eELDBfNkR7sUtYysFZ1Q7BQ1mYt27DMkups/3vu7xgPyOpMD+iAfrBZFzUXT2iw0fmFb8s1gfNBZS+IgjKdQ==", "path": "microsoft.extensions.configuration.binder/3.1.0", "hashPath": "microsoft.extensions.configuration.binder.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-BmANAnR5Xd4Oqw7yQ75xOAYODybZQRzdeNucg7kS5wWKd2PNnMdYtJ2Vciy0QLylRmv42DGl5+AFL9izA6F1Rw==", "path": "microsoft.extensions.dependencyinjection/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-G3i<PERSON>Onn3tETEUvkE9J3a23wQpRkiXZp73zR0XNlicjLFhkeWW1FCaC2bTjrgHhPi2KO6x0BXnHvVuJPIlygBQ==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-LiOP1ceFaPBxaE28SOjtORzOVCJk33TT5VQ/Cg5EoatZh1dxpPAgAV/0ruzWKQE7WAHU3F1H9Z6rFgsQwIb9uQ==", "path": "microsoft.extensions.hosting.abstractions/3.1.0", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-Ef7P8kpJzX/khXB8oYxt3vFHXw48uWY+NirCrh8u8gokCHxr/Noc3OxME+Ji1ugqoMvUVSPu73mYctmUMDlOCA==", "path": "microsoft.extensions.localization/8.0.12", "hashPath": "microsoft.extensions.localization.8.0.12.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-bgwe0gy9v12hr6gLAXIeEWaTYm295Nfmp7B/DLmS75GBJXvs4JHn7pi8gn3jXMNNkNIkx9iWG+pXKmLAmjdGZg==", "path": "microsoft.extensions.localization.abstractions/8.0.12", "hashPath": "microsoft.extensions.localization.abstractions.8.0.12.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "path": "microsoft.extensions.logging.abstractions/8.0.2", "hashPath": "microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "path": "microsoft.extensions.options/8.0.2", "hashPath": "microsoft.extensions.options.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.JSInterop/8.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-zpSj954QMEIc9AHxFKQSP+WXGJSdbrnZrGwJ65vvT2XQGQ7h5rSfif7JLFXhxuuFkRbboTms/Y74yafRhQUd8w==", "path": "microsoft.jsinterop/8.0.12", "hashPath": "microsoft.jsinterop.8.0.12.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/8.0.19": {"type": "package", "serviceable": true, "sha512": "sha512-IhHf+zeZiaE5EXRyxILd4qM+Hj9cxV3sa8MpzZgeEhpvaG3a1VEGF6UCaPFLO44Kua3JkLKluE0SWVamS50PlA==", "path": "microsoft.net.illink.tasks/8.0.19", "hashPath": "microsoft.net.illink.tasks.8.0.19.nupkg.sha512"}, "MudBlazor.JSCompiler/1.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-cWsV0rETNdk6yGkqonfJvuAqgyKU01b8kL2FJg+Xq0v9ZejgsuP2L/vMR/RTZuuhkCQRHPhKFLmpK9YG+vtAWg==", "path": "mudblazor.jscompiler/1.0.17", "hashPath": "mudblazor.jscompiler.1.0.17.nupkg.sha512"}, "MudBlazor.SassCompiler/2.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-l9qO3CQjPpX59ctYEUKLThu/hL4NxvtOaANm+jp0DKbczhOuCZsomD2Vul1UUTSr5km7uNKPQxTej57WqbsqHg==", "path": "mudblazor.sasscompiler/2.0.7", "hashPath": "mudblazor.sasscompiler.2.0.7.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}}}