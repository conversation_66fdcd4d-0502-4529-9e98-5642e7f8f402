{"format": 1, "restore": {"C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor.Analyzers\\MudBlazor.Analyzers.csproj": {}}, "projects": {"C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor.Analyzers\\MudBlazor.Analyzers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor.Analyzers\\MudBlazor.Analyzers.csproj", "projectName": "MudBlazor.Analyzers", "projectPath": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor.Analyzers\\MudBlazor.Analyzers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor.Analyzers\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files (x86)\\Microsoft\\Xamarin\\NuGet\\"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "false", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": {"target": "Package", "version": "[3.11.0, )"}, "Microsoft.CodeAnalysis.CSharp": {"target": "Package", "version": "[4.7.0, )"}, "NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}}