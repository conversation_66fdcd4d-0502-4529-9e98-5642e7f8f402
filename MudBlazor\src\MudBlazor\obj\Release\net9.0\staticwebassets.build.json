{"Version": 1, "Hash": "f8WTZ4nTjUf3IWksLvxO+B42iZlIPQ0D/FjJp4NW2vM=", "Source": "MudBlazor", "BasePath": "_content/MudBlazor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "MudBlazor\\wwwroot", "Source": "MudBlazor", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "SourceId": "MudBlazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint=bj7ppmloxe}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oieqqfqtoo", "Integrity": "AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "FileLength": 11667, "LastWriteTime": "2025-09-09T13:57:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "SourceId": "MudBlazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint=hmu6kx9mgf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "FileLength": 64916, "LastWriteTime": "2025-09-09T13:57:23+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hmu6kx9mgf", "Integrity": "d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MudBlazor.min.css", "FileLength": 595896, "LastWriteTime": "2025-09-09T13:57:15+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bj7ppmloxe", "Integrity": "ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MudBlazor.min.js", "FileLength": 55250, "LastWriteTime": "2025-09-09T13:57:15+00:00"}], "Endpoints": [{"Route": "MudBlazor.min.bj7ppmloxe.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "label", "Value": "MudBlazor.min.js"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "MudBlazor.min.bj7ppmloxe.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "label", "Value": "MudBlazor.min.js"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "MudBlazor.min.bj7ppmloxe.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "label", "Value": "MudBlazor.min.js.gz"}, {"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}]}, {"Route": "MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "MudBlazor.min.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}]}, {"Route": "MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "label", "Value": "MudBlazor.min.css"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "label", "Value": "MudBlazor.min.css"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "MudBlazor.min.hmu6kx9mgf.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "label", "Value": "MudBlazor.min.css.gz"}, {"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}]}, {"Route": "MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "MudBlazor.min.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Release\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 09 Sep 2025 13:57:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}]}]}