﻿<Project>
  <ItemGroup>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.css'))">
      <SourceType>Package</SourceType>
      <SourceId>MudBlazor</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/MudBlazor</BasePath>
      <RelativePath>MudBlazor.min.css</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>hmu6kx9mgf</Fingerprint>
      <Integrity>d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>595896</FileLength>
      <LastWriteTime>Tue, 09 Sep 2025 13:57:15 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.css'))</OriginalItemSpec>
    </StaticWebAsset>
    <StaticWebAsset Include="$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.js'))">
      <SourceType>Package</SourceType>
      <SourceId>MudBlazor</SourceId>
      <ContentRoot>$(MSBuildThisFileDirectory)..\staticwebassets\</ContentRoot>
      <BasePath>_content/MudBlazor</BasePath>
      <RelativePath>MudBlazor.min.js</RelativePath>
      <AssetKind>All</AssetKind>
      <AssetMode>All</AssetMode>
      <AssetRole>Primary</AssetRole>
      <RelatedAsset></RelatedAsset>
      <AssetTraitName></AssetTraitName>
      <AssetTraitValue></AssetTraitValue>
      <Fingerprint>bj7ppmloxe</Fingerprint>
      <Integrity>ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=</Integrity>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      <FileLength>55250</FileLength>
      <LastWriteTime>Tue, 09 Sep 2025 13:57:15 GMT</LastWriteTime>
      <OriginalItemSpec>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.js'))</OriginalItemSpec>
    </StaticWebAsset>
  </ItemGroup>
</Project>