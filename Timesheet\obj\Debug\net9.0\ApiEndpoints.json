[{"ContainingType": "Microsoft.AspNetCore.Routing.IdentityEndpointExtensions+<>c", "Method": "<MapIdentityEndpoints>b__0_0", "RelativePath": "Account/Logout", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Microsoft.AspNetCore.Routing.IdentityEndpointExtensions+<>c", "Method": "<MapIdentityEndpoints>b__0_1", "RelativePath": "Account/PerformGoogleLogin", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Microsoft.AspNetCore.Routing.IdentityEndpointExtensions+<>c", "Method": "<MapIdentityEndpoints>b__0_2", "RelativePath": "Account/PerformMicrosoftLogin", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]