is_global = true
build_property.MudDebugAnalyzer = 
build_property.MudAllowedAttributePattern = 
build_property.MudAllowedAttributeList = 
build_property.TargetFramework = net9.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows,browser
build_property.RootNamespace = MudBlazor.Analyzers.TestComponents
build_property.RootNamespace = MudBlazor.Analyzers.TestComponents
build_property.ProjectDir = C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.Analyzers.TestComponents\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 9.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.Analyzers.TestComponents
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 9.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.Analyzers.TestComponents/AdditonalDiagnostics.razor]
build_metadata.AdditionalFiles.TargetPath = QWRkaXRvbmFsRGlhZ25vc3RpY3MucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.Analyzers.TestComponents/AttributeTest.razor]
build_metadata.AdditionalFiles.TargetPath = QXR0cmlidXRlVGVzdC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.Analyzers.TestComponents/InheritedMudChip.razor]
build_metadata.AdditionalFiles.TargetPath = SW5oZXJpdGVkTXVkQ2hpcC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/OneDrive - PolyExpert inc/Bureau/Timesheet/MudBlazor/src/MudBlazor.Analyzers.TestComponents/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = X0ltcG9ydHMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 
