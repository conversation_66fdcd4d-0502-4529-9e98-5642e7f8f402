﻿@namespace MudBlazor
@using MudBlazor.Utilities
@inherits MudComponentBase

<div @attributes="UserAttributes" class="@Classname" style="@Style">
    @ChildContent
</div>

@code {
#nullable enable
    protected string Classname =>
        new CssBuilder("mud-container")
            .AddClass($"mud-container-fixed", Fixed)
            .AddClass($"mud-container-maxwidth-{MaxWidth.ToDescriptionString()}", !Fixed)
            .AddClass($"mud-container--gutters", Gutters)
            .AddClass(Class)
            .Build();

    /// <summary>
    /// Set the max-width to match the min-width of the current breakpoint. This is useful if you'd prefer to design for a fixed set of sizes instead of trying to accommodate a fully fluid viewport. It's fluid by default.
    /// </summary>
    [Parameter]
    [Category(CategoryTypes.Container.Behavior)]
    public bool Fixed { get; set; } = false;

    /// <summary>
    /// Determine the max-width of the container. The container width grows with the size of the screen. Set to false to disable maxWidth.
    /// </summary>
    [Parameter]
    [Category(CategoryTypes.Container.Behavior)]
    public MaxWidth MaxWidth { get; set; } = MaxWidth.Large;

    /// <summary>
    /// Adds left and right padding to the container itself.
    /// </summary>
    /// <remarks>
    /// Default is <c>true</c>.
    /// </remarks>
    [Parameter]
    [Category(CategoryTypes.Container.Behavior)]
    public bool Gutters { get; set; } = true;

    /// <summary>
    /// Child content of component.
    /// </summary>
    [Parameter]
    [Category(CategoryTypes.Container.Behavior)]
    public RenderFragment? ChildContent { get; set; }
}