is_global = true
build_property.TargetFramework = netstandard2.0
build_property.TargetPlatformMinVersion = 7.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = true
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.MudDebugAnalyzer = 
build_property.MudAllowedAttributePattern = 
build_property.MudAllowedAttributeList = 
build_property.RootNamespace = MudBlazor.Analyzers
build_property.ProjectDir = C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.Analyzers\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.EffectiveAnalysisLevelStyle = 
build_property.EnableCodeStyleSeverity = 
