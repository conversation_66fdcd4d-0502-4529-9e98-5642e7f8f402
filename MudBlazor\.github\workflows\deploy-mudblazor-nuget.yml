name: deploy-mudblazor-nuget

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Tag to publish v[0-9]+.[0-9]+.[0-9]+*'
        required: true
        default: ''
        type: string
  push:
    tags: 
      - "v[0-9]+.[0-9]+.[0-9]+*"

jobs:
  get-version:
    name: Get version to deploy
    runs-on: ubuntu-latest
    env:
      VERSION: 1.0.0
    outputs:
      VERSION: ${{ steps.output-version.outputs.VERSION }}
    steps:
    - name: Set tag from input
      if: ${{ github.event.inputs.version != '' }}
      env:
        TAG: ${{ github.event.inputs.version }}
      run: echo "VERSION=${TAG#v}" >> $GITHUB_ENV

    - name: Set version variable from tag
      if: ${{ github.ref_type == 'tag' }}
      env:
        TAG: ${{ github.ref_name }}
      run: echo "VERSION=${TAG#v}" >> $GITHUB_ENV

    - name: VERSION to job output
      id: output-version
      run: echo "VERSION=${{ env.VERSION }}" >> $GITHUB_OUTPUT

  deploy-nuget:
    name: Deploy mudblazor nuget to nuget.org
    needs: get-version
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        ref: 'refs/tags/v${{ needs.get-version.outputs.VERSION }}'
    - name: Setup dotnet version
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: |
          8.0.x
          9.0.x
    - name: Pack nuget package
      run: dotnet pack -c Release --output nupkgs /p:Version=${{ needs.get-version.outputs.VERSION }}
      working-directory: ./src/MudBlazor
    - name: Check package contains css
      shell: pwsh
      run: ./tools/CheckPackageContainsStaticAssets.ps1 ./src/MudBlazor/nupkgs MudBlazor.min.css
    - name: Check package contains js
      shell: pwsh
      run: ./tools/CheckPackageContainsStaticAssets.ps1 ./src/MudBlazor/nupkgs MudBlazor.min.js
    - name: Publish nuget package
      run: dotnet nuget push nupkgs/*.nupkg -k ${{ secrets.NUGET_KEY }} -s https://api.nuget.org/v3/index.json --skip-duplicate
      working-directory: ./src/MudBlazor
    - name: Push nuget generated package symbols
      run: dotnet nuget push nupkgs/*.snupkg -k ${{ secrets.NUGET_KEY }} -s https://api.nuget.org/v3/index.json --skip-duplicate
      working-directory: ./src/MudBlazor
