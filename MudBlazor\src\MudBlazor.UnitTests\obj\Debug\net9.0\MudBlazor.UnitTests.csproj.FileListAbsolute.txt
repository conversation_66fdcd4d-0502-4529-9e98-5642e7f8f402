C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\.msCoverageSourceRootsMapping_MudBlazor.UnitTests
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\CoverletSourceRootsMapping_MudBlazor.UnitTests
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\testhost.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.staticwebassets.runtime.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Properties\launchSettings.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Analyzers.TestComponents.staticwebassets.runtime.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Analyzers.TestComponents.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.Viewer.staticwebassets.runtime.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.Viewer.staticwebassets.endpoints.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\Microsoft.Build.Locator.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\Microsoft.IO.Redist.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\Newtonsoft.Json.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Buffers.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Collections.Immutable.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.CommandLine.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Memory.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Numerics.Vectors.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Text.Encodings.Web.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Text.Json.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\System.ValueTuple.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\cs\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\de\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\es\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\fr\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\it\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\ja\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\ko\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\pl\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\pt-BR\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\ru\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\tr\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\zh-Hans\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-net472\zh-Hant\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\Microsoft.Build.Locator.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\Newtonsoft.Json.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\System.Collections.Immutable.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\System.CommandLine.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\System.Text.Encodings.Web.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\System.Text.Json.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\cs\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\de\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\es\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\fr\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\it\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\ja\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\ko\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\pl\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\pt-BR\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\ru\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\runtimes\browser\lib\net6.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\tr\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\zh-Hans\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\BuildHost-netcore\zh-Hant\System.CommandLine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\testhost.exe
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\NUnit3.TestAdapter.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\nunit.engine.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\nunit.engine.api.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\nunit.engine.core.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\testcentric.engine.metadata.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\NUnit3.TestAdapter.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.deps.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.runtimeconfig.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\AngleSharp.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\AngleSharp.Css.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\AngleSharp.Diffing.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\FluentAssertions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Bunit.Core.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Bunit.Web.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Castle.Core.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\FluentValidation.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Humanizer.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.ApplicationInsights.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Authorization.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Components.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Components.Authorization.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Components.Forms.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Components.Web.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Components.WebAssembly.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Components.WebAssembly.Authentication.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Metadata.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.AspNetCore.Razor.Language.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Build.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Build.Framework.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Build.Tasks.Core.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Build.Utilities.Core.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.CodeAnalysis.Workspaces.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.CodeAnalysis.ExternalAccess.RazorCompiler.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.VisualStudio.CodeCoverage.Shim.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Localization.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Localization.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Extensions.TimeProvider.Testing.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.JSInterop.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.JSInterop.WebAssembly.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.NET.StringTools.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Testing.Extensions.Telemetry.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Testing.Extensions.TrxReport.Abstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Testing.Extensions.VSTestBridge.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Testing.Platform.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.Testing.Extensions.MSBuild.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.TestPlatform.CoreUtilities.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.TestPlatform.PlatformAbstractions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.TestPlatform.CommunicationUtilities.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.TestPlatform.CrossPlatEngine.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.TestPlatform.Utilities.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.VisualStudio.TestPlatform.Common.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Microsoft.VisualStudio.Setup.Configuration.Interop.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Moq.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\Newtonsoft.Json.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\nunit.framework.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\nunit.framework.legacy.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.CodeDom.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Composition.AttributedModel.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Composition.Convention.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Composition.Hosting.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Composition.Runtime.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Composition.TypedParts.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Reflection.MetadataLoadContext.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Resources.Extensions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Security.Cryptography.Xml.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Security.Permissions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\System.Windows.Extensions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.Testing.Extensions.Telemetry.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.Testing.Extensions.VSTestBridge.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.Testing.Platform.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.Testing.Extensions.MSBuild.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.Messages.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\runtimes\win\lib\net8.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\runtimes\win\lib\net7.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\runtimes\win\lib\net8.0\System.Windows.Extensions.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Analyzers.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Analyzers.TestComponents.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Examples.Data.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.SourceGenerator.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.Shared.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.Viewer.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Analyzers.TestComponents.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Analyzers.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.SourceGenerator.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.Shared.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.UnitTests.Viewer.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.Examples.Data.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\bin\Debug\net9.0\MudBlazor.xml
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.csproj.AssemblyReference.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.AssemblyInfoInputs.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.AssemblyInfo.cs
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.csproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.sourcelink.json
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazo.5F897EA9.Up2Date
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\refint\MudBlazor.UnitTests.dll
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.pdb
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\MudBlazor.UnitTests.genruntimeconfig.cache
C:\Users\<USER>\OneDrive - PolyExpert inc\Bureau\Timesheet\MudBlazor\src\MudBlazor.UnitTests\obj\Debug\net9.0\ref\MudBlazor.UnitTests.dll
