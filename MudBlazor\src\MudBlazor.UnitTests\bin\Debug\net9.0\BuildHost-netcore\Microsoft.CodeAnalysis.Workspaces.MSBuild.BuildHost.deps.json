{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost/4.12.0-3.24572.7": {"dependencies": {"Microsoft.Build.Locator": "1.6.10", "Microsoft.CodeAnalysis.NetAnalyzers": "8.0.0-preview.23468.1", "Microsoft.CodeAnalysis.PerformanceSensitiveAnalyzers": "3.3.4-beta1.22504.1", "Microsoft.DotNet.XliffTasks": "9.0.0-beta.24076.5", "Microsoft.Net.Compilers.Toolset": "4.11.0-2.24270.4", "Microsoft.VisualStudio.Threading.Analyzers": "17.11.20", "Newtonsoft.Json": "13.0.3", "RichCodeNav.EnvVarDump": "0.1.1643-alpha", "Roslyn.Diagnostics.Analyzers": "3.11.0-beta1.24081.1", "System.Collections.Immutable": "8.0.0", "System.CommandLine": "2.0.0-beta4.24324.3", "System.Text.Json": "8.0.5"}, "runtime": {"Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll": {}}, "resources": {"cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "cs"}, "de/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "de"}, "es/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "es"}, "fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "fr"}, "it/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "it"}, "ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ja"}, "ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ko"}, "pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "ru"}, "tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Build.Locator/1.6.10": {"runtime": {"lib/net6.0/Microsoft.Build.Locator.dll": {"assemblyVersion": "*******", "fileVersion": "1.6.10.57384"}}}, "Microsoft.CodeAnalysis.BannedApiAnalyzers/3.11.0-beta1.24081.1": {}, "Microsoft.CodeAnalysis.NetAnalyzers/8.0.0-preview.23468.1": {}, "Microsoft.CodeAnalysis.PerformanceSensitiveAnalyzers/3.3.4-beta1.22504.1": {}, "Microsoft.CodeAnalysis.PublicApiAnalyzers/3.11.0-beta1.24081.1": {}, "Microsoft.DotNet.XliffTasks/9.0.0-beta.24076.5": {}, "Microsoft.Net.Compilers.Toolset/4.11.0-2.24270.4": {}, "Microsoft.VisualStudio.Threading.Analyzers/17.11.20": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "RichCodeNav.EnvVarDump/0.1.1643-alpha": {}, "Roslyn.Diagnostics.Analyzers/3.11.0-beta1.24081.1": {"dependencies": {"Microsoft.CodeAnalysis.BannedApiAnalyzers": "3.11.0-beta1.24081.1", "Microsoft.CodeAnalysis.PublicApiAnalyzers": "3.11.0-beta1.24081.1"}}, "System.Collections.Immutable/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.CommandLine/2.0.0-beta4.24324.3": {"dependencies": {"System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.CommandLine.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.24.32403"}}, "resources": {"lib/netstandard2.0/cs/System.CommandLine.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.CommandLine.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.CommandLine.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.CommandLine.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.CommandLine.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.CommandLine.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.CommandLine.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.CommandLine.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.CommandLine.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.CommandLine.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.CommandLine.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.CommandLine.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.CommandLine.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Memory/4.5.5": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/8.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Text.Json/8.0.5": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}}}, "libraries": {"Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost/4.12.0-3.24572.7": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Build.Locator/1.6.10": {"type": "package", "serviceable": true, "sha512": "sha512-DJhCkTGqy1LMJzEmG/2qxRTMHwdPc3WdVoGQI5o5mKHVo4dsHrCMLIyruwU/NSvPNSdvONlaf7jdFXnAMuxAuA==", "path": "microsoft.build.locator/1.6.10", "hashPath": "microsoft.build.locator.1.6.10.nupkg.sha512"}, "Microsoft.CodeAnalysis.BannedApiAnalyzers/3.11.0-beta1.24081.1": {"type": "package", "serviceable": true, "sha512": "sha512-DH6L3rsbjppLrHM2l2/NKbnMaYd0NFHx2pjZaFdrVcRkONrV3i9FHv6Id8Dp6/TmjhXQsJVJJFbhhjkpuP1xxg==", "path": "microsoft.codeanalysis.bannedapianalyzers/3.11.0-beta1.24081.1", "hashPath": "microsoft.codeanalysis.bannedapianalyzers.3.11.0-beta1.24081.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.NetAnalyzers/8.0.0-preview.23468.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZhIvyxmUCqb8OiU/VQfxfuAmIB4lQsjqhMVYKeoyxzSI+d7uR5Pzx3ZKoaIhPizQ15wa4lnyD6wg3TnSJ6P4LA==", "path": "microsoft.codeanalysis.netanalyzers/8.0.0-preview.23468.1", "hashPath": "microsoft.codeanalysis.netanalyzers.8.0.0-preview.23468.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.PerformanceSensitiveAnalyzers/3.3.4-beta1.22504.1": {"type": "package", "serviceable": true, "sha512": "sha512-2XRlqPAzVke7Sb80+UqaC7o57OwfK+tIr+aIOxrx41RWDMeR2SBUW7kL4sd6hfLFfBNsLo3W5PT+UwfvwPaOzA==", "path": "microsoft.codeanalysis.performancesensitiveanalyzers/3.3.4-beta1.22504.1", "hashPath": "microsoft.codeanalysis.performancesensitiveanalyzers.3.3.4-beta1.22504.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.PublicApiAnalyzers/3.11.0-beta1.24081.1": {"type": "package", "serviceable": true, "sha512": "sha512-3bYGBihvoNO0rhCOG1U9O50/4Q8suZ+glHqQLIAcKvnodSnSW+dYWYzTNb1UbS8pUS8nAUfxSFMwuMup/G5DtQ==", "path": "microsoft.codeanalysis.publicapianalyzers/3.11.0-beta1.24081.1", "hashPath": "microsoft.codeanalysis.publicapianalyzers.3.11.0-beta1.24081.1.nupkg.sha512"}, "Microsoft.DotNet.XliffTasks/9.0.0-beta.24076.5": {"type": "package", "serviceable": true, "sha512": "sha512-UCm6/kW8+BMSjIUJv4Ep/HkB9MTj95o9uTt3IGqUclAuAqJDqE25YFzV2qi1CrUtu0RRSIUAv9GTs1O35mZ7/A==", "path": "microsoft.dotnet.xlifftasks/9.0.0-beta.24076.5", "hashPath": "microsoft.dotnet.xlifftasks.9.0.0-beta.24076.5.nupkg.sha512"}, "Microsoft.Net.Compilers.Toolset/4.11.0-2.24270.4": {"type": "package", "serviceable": true, "sha512": "sha512-niz/TRxQFG2AtC7qFxMQM1hCkBQwaY2Af/VWgWzrz30mx03cNC4QEor8/uKa45il4kwQx2m8z1xGq7CD8REb5g==", "path": "microsoft.net.compilers.toolset/4.11.0-2.24270.4", "hashPath": "microsoft.net.compilers.toolset.4.11.0-2.24270.4.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Analyzers/17.11.20": {"type": "package", "serviceable": true, "sha512": "sha512-yI80R8Ja4ipU7/fyVAMCu0oGTN/u97OgL99gd/8ycGnCF58Kn93iBU3T7rkxGMnlsHf5vEgRu95dujtYcgwf9Q==", "path": "microsoft.visualstudio.threading.analyzers/17.11.20", "hashPath": "microsoft.visualstudio.threading.analyzers.17.11.20.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mbJSvHfRxfX3tR/U6n1WU+mWHXswYc+SB/hkOpx8yZZe68hNZGfymJu0cjsaJEkVzCMqePiU6LdIyogqfIn7kg==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "RichCodeNav.EnvVarDump/0.1.1643-alpha": {"type": "package", "serviceable": true, "sha512": "sha512-JYCyCxU/KrMUECak2zZoAL+e5lpefBz3Y+qudqUH5n4HLFq+pdju3V0642MWo6WGRtDcAAz7mVdIyfBuqI8DuA==", "path": "richcodenav.envvardump/0.1.1643-alpha", "hashPath": "richcodenav.envvardump.0.1.1643-alpha.nupkg.sha512"}, "Roslyn.Diagnostics.Analyzers/3.11.0-beta1.24081.1": {"type": "package", "serviceable": true, "sha512": "sha512-reHqZCDKifA+DURcL8jUfYkMGL4FpgNt5LI0uWTS6IpM8kKVbu/kO8byZsqfhBu4wUzT3MBDcoMfzhZPdENIpg==", "path": "roslyn.diagnostics.analyzers/3.11.0-beta1.24081.1", "hashPath": "roslyn.diagnostics.analyzers.3.11.0-beta1.24081.1.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.CommandLine/2.0.0-beta4.24324.3": {"type": "package", "serviceable": true, "sha512": "sha512-UddDcdPmeAbRpkYHOMBHYS74UINTL5yXhzHTbruhVN7eR79J8RPGgq1nNXlz5TH4iMK/Fn6OlSFf4Z2UxCJpXw==", "path": "system.commandline/2.0.0-beta4.24324.3", "hashPath": "system.commandline.2.0.0-beta4.24324.3.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1AVzAb5OxJNvJLnOADtexNmWgattm2XVOT3TjQTN7Dd4SqoSwai1CsN2fth42uQldJSQdz/sAec0+TzxBFgisw==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}}}