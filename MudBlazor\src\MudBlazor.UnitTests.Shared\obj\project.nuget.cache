{"version": 2, "dgSpecHash": "V2po7m86i9Y=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor.UnitTests.Shared\\MudBlazor.UnitTests.Shared.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\anglesharp\\1.2.0\\anglesharp.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\anglesharp.css\\1.0.0-beta.144\\anglesharp.css.1.0.0-beta.144.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\anglesharp.diffing\\1.0.0\\anglesharp.diffing.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bunit\\1.38.5\\bunit.1.38.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bunit.core\\1.38.5\\bunit.core.1.38.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bunit.web\\1.38.5\\bunit.web.1.38.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authorization\\9.0.1\\microsoft.aspnetcore.authorization.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components\\9.0.1\\microsoft.aspnetcore.components.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.analyzers\\9.0.1\\microsoft.aspnetcore.components.analyzers.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.authorization\\9.0.0\\microsoft.aspnetcore.components.authorization.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.forms\\9.0.1\\microsoft.aspnetcore.components.forms.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.web\\9.0.1\\microsoft.aspnetcore.components.web.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly\\9.0.0\\microsoft.aspnetcore.components.webassembly.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.components.webassembly.authentication\\9.0.0\\microsoft.aspnetcore.components.webassembly.authentication.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.metadata\\9.0.1\\microsoft.aspnetcore.metadata.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.0\\microsoft.extensions.caching.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.0\\microsoft.extensions.caching.memory.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.0\\microsoft.extensions.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.0\\microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.0\\microsoft.extensions.configuration.binder.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.0\\microsoft.extensions.configuration.fileextensions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.0\\microsoft.extensions.configuration.json.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.1\\microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.1\\microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.0\\microsoft.extensions.fileproviders.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.0\\microsoft.extensions.fileproviders.physical.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.0\\microsoft.extensions.filesystemglobbing.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization\\9.0.1\\microsoft.extensions.localization.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.localization.abstractions\\9.0.1\\microsoft.extensions.localization.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.0\\microsoft.extensions.logging.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.1\\microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.1\\microsoft.extensions.options.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.1\\microsoft.extensions.primitives.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop\\9.0.1\\microsoft.jsinterop.9.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.jsinterop.webassembly\\9.0.0\\microsoft.jsinterop.webassembly.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nunit\\4.2.2\\nunit.4.2.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.0\\system.text.json.9.0.0.nupkg.sha512"], "logs": []}