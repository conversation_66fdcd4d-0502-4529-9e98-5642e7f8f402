@using Microsoft.AspNetCore.Components.Sections;

@namespace MudB<PERSON><PERSON>
@inherits MudComponentBase

@if (Contextual)
{
    <SectionOutlet SectionId="@ContextualActionBar" />
    <SectionContent SectionId="@ContextualActionBar">
        @(Bottom ? Footer : Header)
    </SectionContent>
}
else
{
    @(Bottom ? Footer : Header)
}

@code {
    private RenderFragment Header =>
        @<header @attributes="@UserAttributes" class="@Classname" style="@Style">
            <MudToolBar Dense="@Dense" Gutters="@Gutters" Class="@ToolBarClassname" WrapContent="@WrapContent">
                @ChildContent
            </MudToolBar>
        </header>;

    private RenderFragment Footer =>
        @<footer @attributes="@UserAttributes" class="@Classname" style="@Style">
            <MudToolBar Dense="@Dense" Gutters="@Gutters" Class="@ToolBarClassname" WrapContent="@WrapContent">
                @ChildContent
            </MudToolBar>
        </footer>;
}