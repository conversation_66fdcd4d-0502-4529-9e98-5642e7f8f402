{"Version": 1, "Hash": "NjfvoThC4kO8oXhiPqyDnK+uhrU8qU++oDeERyxUSUk=", "Source": "MudBlazor.Analyzers.TestComponents", "BasePath": "_content/MudBlazor.Analyzers.TestComponents", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\MudBlazor.csproj", "Version": 2, "Source": "MudBlazor", "GetPublishAssetsTargets": "ComputeReferencedStaticWebAssetsPublishManifest;GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "TargetFramework=net9.0", "AdditionalPublishPropertiesToRemove": "RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "TargetFramework=net9.0", "AdditionalBuildPropertiesToRemove": "RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [{"Name": "MudBlazor\\wwwroot", "Source": "MudBlazor", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint=bj7ppmloxe}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oieqqfqtoo", "Integrity": "AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "FileLength": 11667, "LastWriteTime": "2025-08-15T14:09:40+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint=hmu6kx9mgf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integrity": "JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "FileLength": 64916, "LastWriteTime": "2025-08-15T14:09:40+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hmu6kx9mgf", "Integrity": "d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MudBlazor.min.css", "FileLength": 595896, "LastWriteTime": "2025-08-06T21:33:42+00:00"}, {"Identity": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "SourceId": "MudBlazor", "SourceType": "Project", "ContentRoot": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\", "BasePath": "_content/MudBlazor", "RelativePath": "MudBlazor.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "Server", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bj7ppmloxe", "Integrity": "ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\MudBlazor.min.js", "FileLength": 55250, "LastWriteTime": "2025-08-06T21:33:42+00:00"}], "Endpoints": [{"Route": "_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bj7ppmloxe"}, {"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js.gz"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015404285"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "W/\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "595896"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\9dq6273bka-hmu6kx9mgf.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "64916"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hmu6kx9mgf"}, {"Name": "integrity", "Value": "sha256-JNY8pwkIthTqoEzFgsZr4+xqfICooLXKLyhssSEuURk="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css.gz"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000085704491"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\wwwroot\\MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "55250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c=\""}, {"Name": "Last-Modified", "Value": "Wed, 06 Aug 2025 21:33:42 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZxMYXdGvG68LxMTAoTTaLf++nTS/uJ9kk+0w4nTG24c="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "C:\\Users\\<USER>\\OneDrive - PolyExpert inc\\Bureau\\Timesheet\\MudBlazor\\src\\MudBlazor\\obj\\Debug\\net9.0\\compressed\\62csr4rore-bj7ppmloxe.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "11667"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo=\""}, {"Name": "Last-Modified", "Value": "Fri, 15 Aug 2025 14:09:40 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AKJ5EgPfJONNDZeKbWuCAaUY64hiUOvedaF5Zpo8ERo="}]}]}