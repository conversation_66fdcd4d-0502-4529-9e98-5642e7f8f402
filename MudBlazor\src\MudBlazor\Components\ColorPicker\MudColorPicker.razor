﻿@namespace MudBlazor
@using MudBlazor.Resources
@using MudBlazor.Utilities
@inherits MudPicker<MudColor>
@inject InternalMudLocalizer Localizer

@Render

@code {

    protected override RenderFragment PickerContent =>
    @<CascadingValue Value="@this" IsFixed="true">
        <MudPickerToolbar ShowToolbar="@ShowToolbar" Class="mud-picker-color-toolbar">
            @if (PickerVariant != PickerVariant.Static)
            {
                <MudIconButton Class="pa-1 mud-close-picker-button"
                               Size="Size.Small"
                               Color="Color.Inherit"
                               Icon="@CloseIcon"
                               OnClick="@GetEventCallback()"
                               aria-label="@Localizer[LanguageResource.MudColorPicker_Close]" />
            }
            <MudSpacer />
            <MudIconButton Class="pa-1"
                           Size="Size.Small"
                           Color="GetButtonColor(ColorPickerView.Spectrum)"
                           Icon="@SpectrumIcon"
                           OnClick="() => ChangeView(ColorPickerView.Spectrum)"
                           aria-label="@Localizer[LanguageResource.MudColorPicker_SpectrumView]" />
            <MudIconButton Class="pa-1 mx-1"
                           Size="Size.Small"
                           Color="GetButtonColor(ColorPickerView.Grid)"
                           Icon="@GridIcon"
                           OnClick="() => ChangeView(ColorPickerView.Grid)"
                           aria-label="@Localizer[LanguageResource.MudColorPicker_GridView]" />
            <MudIconButton Class="pa-1"
                           Size="Size.Small"
                           Color="GetButtonColor(ColorPickerView.Palette)"
                           Icon="@PaletteIcon"
                           OnClick="() => ChangeView(ColorPickerView.Palette)"
                           aria-label="@Localizer[LanguageResource.MudColorPicker_PaletteView]" />
        </MudPickerToolbar>
        <MudPickerContent Class="mud-picker-color-content">
            @if (ShowColorField)
            {
                <div class="mud-picker-color-picker">
                    @if (_activeColorPickerView == ColorPickerView.Spectrum)
                    {
                        <div class="mud-picker-color-overlay" style="@($"background-color: {_baseColor?.ToString(MudColorOutputFormats.RGB)}")">
                            <div class="mud-picker-color-overlay mud-picker-color-overlay-white">
                                <div class="mud-picker-color-overlay mud-picker-color-overlay-black">
                                    <div class="mud-picker-color-overlay"
                                         id="@_id"
                                         @onpointerdown="OnColorOverlayClick"
                                         @onpointerup="OnColorOverlayClick"
                                         @onpointermove="OnPointerMoveAsync">
                                        <svg class="mud-picker-color-selector"
                                             height="26"
                                             width="26"
                                             style="transform: @GetSelectorLocation()">
                                            <defs>
                                                <filter id="mud-picker-color-selector-shadow" x="-50%" y="-50%" width="200%" height="200%">
                                                    <feGaussianBlur in="SourceAlpha" stdDeviation="1" />
                                                    <feOffset dx="0" dy="5" result="offsetblur" />
                                                    <feOffset dx="0" dy="-5" result="offsetblur" />
                                                    <feMerge>
                                                        <feMergeNode />
                                                        <feMergeNode in="SourceGraphic" />
                                                    </feMerge>
                                                </filter>
                                            </defs>
                                            <circle r="10" cx="13" cy="13" stroke="white" stroke-width="1" fill="transparent" style="filter: url(#mud-picker-color-selector-shadow)" />
                                            <circle r="11" cx="13" cy="13" stroke="white" stroke-width="1.5" fill="transparent" />
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    @if (_activeColorPickerView is ColorPickerView.Grid or ColorPickerView.GridCompact)
                    {
                        <div class="mud-picker-color-grid">
                            @foreach (var item in _gridList)
                            {
                                <div class="@GetColorDotClass(item)"
                                     style="@($"background: {item.ToString(MudColorOutputFormats.RGBA)};")"
                                     @onclick="@GetSelectPaletteColorCallback(item)"
                                     aria-label="@Localizer[LanguageResource.MudColorPicker_ColorDot]"></div>
                            }
                        </div>
                    }
                </div>
            }
            @if (_activeColorPickerView != ColorPickerView.GridCompact)
            {
                @if (ShowSliders || ShowInputs || ShowPreview)
                {
                    <div class="mud-picker-color-controls">
                        @if (ShowSliders || ShowPreview)
                        {
                            <div class="mud-picker-color-controls-row">
                                @if (ShowPreview)
                                {
                                    <div class="mud-picker-color-dot mud-picker-color-dot-current mud-ripple"
                                         @onclick="ToggleCollection"
                                         aria-label="@Localizer[LanguageResource.MudColorPicker_ToggleCurrentColor]">
                                        <div class="mud-picker-color-fill" style="@($"background: {_value?.ToString(MudColorOutputFormats.RGBA)};")"></div>
                                    </div>
                                }
                                @if (ShowSliders && !_collectionOpen)
                                {
                                    <div class="mud-picker-color-sliders">
                                        @if (_activeColorPickerView != ColorPickerView.Grid)
                                        {
                                            <MudSlider Class="mud-picker-color-slider hue"
                                                       dir="ltr"
                                                       T="int"
                                                       Value="(int)_value.H"
                                                       ValueChanged="UpdateBaseColorSlider"
                                                       Step="1"
                                                       Min="0"
                                                       Max="360"
                                                       aria-label="@Localizer[LanguageResource.MudColorPicker_HueSlider]" />
                                        }
                                        @if (ShowAlpha)
                                        {
                                            <MudSlider Class="mud-picker-color-slider alpha"
                                                       Value="_value.A"
                                                       ValueChanged="SetAlpha"
                                                       T="int"
                                                       Min="0"
                                                       Max="255"
                                                       Step="1"
                                                       Style="@AlphaSliderStyle"
                                                       aria-label="@Localizer[LanguageResource.MudColorPicker_AlphaSlider]" />
                                        }
                                    </div>
                                }
                                @if (_collectionOpen)
                                {
                                    <div class="mud-picker-color-collection">
                                        @foreach (var item in Palette.Take(5))
                                        {
                                            <div class="@GetColorDotClass(item)"
                                                 style="@($"background: {item.ToString(MudColorOutputFormats.RGBA)};")"
                                                 @onclick="@GetSelectPaletteColorCallback(item)"
                                                 aria-label="@Localizer[LanguageResource.MudColorPicker_PaletteColor]"></div>
                                        }
                                    </div>
                                }
                            </div>
                        }
                        @if (ShowInputs)
                        {
                            <div class="mud-picker-color-controls-row">
                                <div class="mud-picker-color-inputs">
                                    @switch (ColorPickerMode)
                                    {
                                        case ColorPickerMode.RGB:
                                            <MudNumericField T="int"
                                                             Value="_value.R"
                                                             ValueChanged="SetR"
                                                             Class="mud-picker-color-inputfield"
                                                             HelperText="R"
                                                             Min="0"
                                                             Max="255"
                                                             Variant="Variant.Outlined"
                                                             HideSpinButtons="true"
                                                             Required="@Required" />
                                            <MudNumericField T="int"
                                                             Value="_value.G"
                                                             ValueChanged="SetG"
                                                             Class="mud-picker-color-inputfield"
                                                             HelperText="G"
                                                             Min="0"
                                                             Max="255"
                                                             Variant="Variant.Outlined"
                                                             HideSpinButtons="true"
                                                             Required="@Required" />
                                            <MudNumericField T="int"
                                                             Value="_value.B"
                                                             ValueChanged="SetB"
                                                             Class="mud-picker-color-inputfield"
                                                             HelperText="B"
                                                             Min="0"
                                                             Max="255"
                                                             Variant="Variant.Outlined"
                                                             HideSpinButtons="true"
                                                             Required="@Required" />
                                            break;
                                        case ColorPickerMode.HSL:
                                            <MudNumericField T="double"
                                                             Value="@_value.H"
                                                             ValueChanged="SetH"
                                                             Class="mud-picker-color-inputfield"
                                                             HelperText="H"
                                                             Step="1"
                                                             Min="0"
                                                             Max="360"
                                                             Variant="Variant.Outlined"
                                                             HideSpinButtons="true"
                                                             Required="@Required" />
                                            <MudNumericField T="double"
                                                             Value="@_value.S"
                                                             ValueChanged="SetS"
                                                             Class="mud-picker-color-inputfield"
                                                             HelperText="S"
                                                             Step="0.01"
                                                             Min="0"
                                                             Max="100"
                                                             Variant="Variant.Outlined"
                                                             HideSpinButtons="true"
                                                             Required="@Required" />
                                            <MudNumericField T="double"
                                                             Value="@_value.L"
                                                             ValueChanged="SetL"
                                                             Class="mud-picker-color-inputfield"
                                                             HelperText="L"
                                                             Step="0.01"
                                                             Min="0"
                                                             Max="100"
                                                             Variant="Variant.Outlined"
                                                             HideSpinButtons="true"
                                                             Required="@Required" />
                                            break;
                                        case ColorPickerMode.HEX:
                                            <MudTextField T="string"
                                                          Value="@GetColorTextValue()"
                                                          ValueChanged="SetInputString"
                                                          Class="mud-picker-color-inputfield"
                                                          Variant="Variant.Outlined"
                                                          MaxLength="@GetHexColorInputMaxLength()"
                                                          HelperText="HEX"
                                                          Required="@Required" />
                                            break;
                                    }

                                    @if (ShowAlpha && ColorPickerMode != ColorPickerMode.HEX)
                                    {
                                        <MudNumericField T="double"
                                                         Value="@(Math.Round(_value.A / 255.0, 2))"
                                                         ValueChanged="SetAlpha"
                                                         Class="mud-picker-color-inputfield input-field-alpha"
                                                         HelperText="A"
                                                         Min="0"
                                                         Max="1"
                                                         Step="0.01"
                                                         Variant="Variant.Outlined"
                                                         HideSpinButtons="true"
                                                         Required="@Required" />
                                    }
                                </div>
                                @if (ShowModeSwitch)
                                {
                                    <div class="mud-picker-control-switch">
                                        <MudIconButton OnClick="ChangeMode"
                                                       Icon="@ImportExportIcon"
                                                       Class="pa-1 me-n1"
                                                       aria-label="@Localizer[LanguageResource.MudColorPicker_ModeSwitch]"></MudIconButton>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                }
                @if (_activeColorPickerView == ColorPickerView.Palette)
                {
                    <div class="mud-picker-color-view">
                        <div class="mud-picker-color-view-collection">
                            @foreach (var item in Palette)
                            {
                                <div class="@GetColorDotClass(item)"
                                     style="@($"background: {item.ToString(MudColorOutputFormats.RGBA)};")"
                                     @onclick="@GetSelectPaletteColorCallback(item)"
                                     aria-label="@Localizer[LanguageResource.MudColorPicker_PaletteColor]"></div>
                            }
                        </div>
                    </div>
                }
            }
        </MudPickerContent>
    </CascadingValue>;
}
