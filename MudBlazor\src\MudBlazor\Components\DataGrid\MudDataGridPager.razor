﻿@namespace MudBlazor
@inherits MudComponentBase
@typeparam T
@using MudBlazor.Resources
@inject InternalMudLocalizer Localizer

<MudToolBar @attributes="UserAttributes" Class="@Classname" Style="@Style">
    <div class="mud-table-pagination-spacer"></div>
    @if (PageSizeSelector)
    {
        <MudText Typo="Typo.body2" Class="mud-table-pagination-caption">
            @RowsPerPageString
        </MudText>
        <MudSelect T="int" ValueChanged="@SetRowsPerPageAsync" Value="@(DataGrid?.RowsPerPage ?? 0)" Class="mud-table-pagination-select" Underline="false" Dense="true" Disabled="@Disabled">
            @foreach (int pageSize in PageSizeOptions)
            {
                if (pageSize == int.MaxValue)
                {
                    <MudSelectItem T="int" Value="@pageSize">@AllItemsText</MudSelectItem>
                }
                else 
                {
                    <MudSelectItem T="int" Value="@pageSize">@pageSize.ToString().ToUpper()</MudSelectItem>
                }
            }
        </MudSelect>
    }

    @if (ShowPageNumber)
    {
        <MudText Typo="Typo.body2" Class="mud-table-pagination-caption">
            @Info
        </MudText>
    }
    
    @if (ShowNavigation)
    {
        <div class="mud-table-pagination-actions">
            <MudIconButton Class="mud-flip-x-rtl" Icon="@Icons.Material.Filled.FirstPage" Disabled="@BackButtonsDisabled" @onclick="@(() => DataGrid.NavigateTo(Page.First))" aria-label="@Localizer[LanguageResource.MudDataGridPager_FirstPage]" />
            <MudIconButton Class="mud-flip-x-rtl" Icon="@Icons.Material.Filled.NavigateBefore" Disabled="@BackButtonsDisabled" @onclick="@(() => DataGrid.NavigateTo(Page.Previous))" aria-label="@Localizer[LanguageResource.MudDataGridPager_PreviousPage]" />
            <MudIconButton Class="mud-flip-x-rtl" Icon="@Icons.Material.Filled.NavigateNext" Disabled="@ForwardButtonsDisabled" @onclick="@(() => DataGrid.NavigateTo(Page.Next))" aria-label="@Localizer[LanguageResource.MudDataGridPager_NextPage]" />
            <MudIconButton Class="mud-flip-x-rtl" Icon="@Icons.Material.Filled.LastPage" Disabled="@ForwardButtonsDisabled" @onclick="@(() => DataGrid.NavigateTo(Page.Last))" aria-label="@Localizer[LanguageResource.MudDataGridPager_LastPage]" />
        </div>
    }

</MudToolBar>

