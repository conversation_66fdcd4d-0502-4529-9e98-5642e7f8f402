{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "clean",
            "command": "dotnet",
            "type": "shell",
            "args": [
                "clean",
                "src/MudBlazor.sln",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "group": "build",
            "problemMatcher": "$msCompile"
        },
        {
            "label": "build",
            "command": "dotnet",
            "type": "shell",
            "args": [
                "build",
                "src/MudBlazor.sln",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": "$msCompile"
        },
        {
            "label": "test",
            "command": "dotnet",
            "type": "shell",
            "args": [
                "test",
                "src/MudBlazor.UnitTests/MudBlazor.UnitTests.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary",
                "/p:CollectCoverage=true",
                "/p:CoverletOutputFormat=cobertura",
                "/p:ExcludeByAttribute='ExcludeFromCodeCoverageAttribute'",
                "/p:ExcludeByFile='**/*.g.cs'", 
                "/p:Include='[MudBlazor]*'",
                "/p:SkipAutoProps=true",
                "/p:CoverletOutput='./TestResults/'"
            ],
            "group": "build",
            "problemMatcher": "$msCompile"
        },
        {
            "label": "build and test",
            "dependsOrder": "sequence",
            "dependsOn": [
                "build",
                "test"
            ],
            "problemMatcher": [
                "$msCompile"
            ]
        },
        {
            "label": "coverage report",
            "command": "open",
            "type": "shell",
            "args": [
                "src/MudBlazor.UnitTests/TestResults/Report/Index.html",
            ],
            "problemMatcher": []
        },
        {
            "label": "docs generator",
            "command": "dotnet",
            "type": "shell",
            "args": [
                "run",
                "--project",
                "src/MudBlazor.Docs.Compiler/MudBlazor.Docs.Compiler.csproj",
                "/property:GenerateFullPaths=true",
                "/consoleloggerparameters:NoSummary"
            ],
            "group": "build",
            "problemMatcher": "$msCompile"
        }
    ]
}