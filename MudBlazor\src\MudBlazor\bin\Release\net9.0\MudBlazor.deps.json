{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"MudBlazor/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Web": "9.0.1", "Microsoft.CodeAnalysis.ResxSourceGenerator": "3.11.0-beta1.24605.2", "Microsoft.Extensions.Localization": "9.0.1", "Microsoft.NET.ILLink.Tasks": "9.0.8", "MudBlazor.JSCompiler": "1.0.17", "MudBlazor.SassCompiler": "2.0.7"}, "runtime": {"MudBlazor.dll": {}}}, "Microsoft.AspNetCore.Authorization/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.1", "Microsoft.AspNetCore.Components.Analyzers": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.1": {}, "Microsoft.AspNetCore.Components.Forms/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Components.Web/9.0.1": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.1", "Microsoft.AspNetCore.Components.Forms": "9.0.1", "Microsoft.Extensions.DependencyInjection": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1", "Microsoft.JSInterop": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.AspNetCore.Metadata/9.0.1": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.CodeAnalysis.ResxSourceGenerator/3.11.0-beta1.24605.2": {}, "Microsoft.Extensions.Configuration/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Hosting.Abstractions/3.1.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.FileProviders.Abstractions": "3.1.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.1"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "3.100.19.56504"}}}, "Microsoft.Extensions.Localization/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Localization.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.1", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Options/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Primitives": "9.0.1"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.Extensions.Primitives/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61010"}}}, "Microsoft.JSInterop/9.0.1": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.NET.ILLink.Tasks/9.0.8": {}, "MudBlazor.JSCompiler/1.0.17": {"runtime": {"lib/netstandard2.0/MudBlazor.JSCompiler.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MudBlazor.SassCompiler/2.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.1", "Microsoft.Extensions.Hosting.Abstractions": "3.1.0", "Microsoft.Extensions.Options": "9.0.1"}, "runtime": {"lib/net6.0/AspNetCore.SassCompiler.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"MudBlazor/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-WgLlLBlMczb2+QLNG6sM95OUZ0EBztz60k/N75tjIgpyu0SdpIfYytAmX/7JJAjRTZF0c/CrWaQV+SH9FuGsrA==", "path": "microsoft.aspnetcore.authorization/9.0.1", "hashPath": "microsoft.aspnetcore.authorization.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6pwfbQKNtvPkbF4tCGiAKGyt6BVpu58xAXz7u2YXcUKTNmNxrymbG1mEyMc0EPzVdnquDDqTyfXM3mC1EJycxQ==", "path": "microsoft.aspnetcore.components/9.0.1", "hashPath": "microsoft.aspnetcore.components.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-I8Rs4LXT5UQxM5Nin2+Oj8aSY2heszSZ3EyTLgt3mxmfiRPrVO7D8NNSsf1voI2Gb0qFJceof/J5c9E+nfNuHw==", "path": "microsoft.aspnetcore.components.analyzers/9.0.1", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KyULVU32bLz74LWDwPEwNUEllTehzWJuM7YAsz80rMKEzvR0K8cRjRzO0fnN/nfydMeLRRlbI0xj8wnEAymLVw==", "path": "microsoft.aspnetcore.components.forms/9.0.1", "hashPath": "microsoft.aspnetcore.components.forms.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-LI0vjYEd9MaDZPDQxPCn4gGYDkEC5U9rp1nWZo7rPozJxgTG2zU3WERujxTi2LeAC2ZzdXlOVCrUyPQ55LZV2A==", "path": "microsoft.aspnetcore.components.web/9.0.1", "hashPath": "microsoft.aspnetcore.components.web.9.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-EZnHifamF7IFEIyjAKMtJM3I/94OIe72i3P09v5oL0twmsmfQwal6Ni3m8lbB5mge3jWFhMozeW+rUdRSqnXRQ==", "path": "microsoft.aspnetcore.metadata/9.0.1", "hashPath": "microsoft.aspnetcore.metadata.9.0.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.ResxSourceGenerator/3.11.0-beta1.24605.2": {"type": "package", "serviceable": true, "sha512": "sha512-8knaIsNJLuapK1LDTVISNBiU0uKLsVLU/ooCC7uPieEyrv61txxtb0pIfZy7+oKhwnpK8oFaEF10ftBRBxcCig==", "path": "microsoft.codeanalysis.resxsourcegenerator/3.11.0-beta1.24605.2", "hashPath": "microsoft.codeanalysis.resxsourcegenerator.3.11.0-beta1.24605.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Lu41BWNmwhKr6LgyQvcYBOge0pPvmiaK8R5UHXX4//wBhonJyWcT2OK1mqYfEM5G7pTf31fPrpIHOT6sN7EGOA==", "path": "microsoft.extensions.configuration/3.1.0", "hashPath": "microsoft.extensions.configuration.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ESz6bVoDQX7sgWdKHF6G9Pq672T8k+19AFb/txDXwdz7MoqaNQj2/in3agm/3qae9V+WvQZH86LLTNVo0it8vQ==", "path": "microsoft.extensions.configuration.abstractions/3.1.0", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-o9eELDBfNkR7sUtYysFZ1Q7BQ1mYt27DMkups/3vu7xgPyOpMD+iAfrBZFzUXT2iw0fmFb8s1gfNBZS+IgjKdQ==", "path": "microsoft.extensions.configuration.binder/3.1.0", "hashPath": "microsoft.extensions.configuration.binder.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qZI42ASAe3hr2zMSA6UjM92pO1LeDq5DcwkgSowXXPY8I56M76pEKrnmsKKbxagAf39AJxkH2DY4sb72ixyOrg==", "path": "microsoft.extensions.dependencyinjection/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tr74eP0oQ3AyC24ch17N8PuEkrPbD0JqIfENCYqmgKYNOmL8wQKzLJu3ObxTUDrjnn4rHoR1qKa37/eQyHmCDA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-G3i<PERSON>Onn3tETEUvkE9J3a23wQpRkiXZp73zR0XNlicjLFhkeWW1FCaC2bTjrgHhPi2KO6x0BXnHvVuJPIlygBQ==", "path": "microsoft.extensions.fileproviders.abstractions/3.1.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-LiOP1ceFaPBxaE28SOjtORzOVCJk33TT5VQ/Cg5EoatZh1dxpPAgAV/0ruzWKQE7WAHU3F1H9Z6rFgsQwIb9uQ==", "path": "microsoft.extensions.hosting.abstractions/3.1.0", "hashPath": "microsoft.extensions.hosting.abstractions.3.1.0.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UgvX4Yb2T3tEsKT30ktZr0H7kTRPapCgEH0bdTwxiEGSdA39/hAQMvvb+vgHpqmevDU5+puyI9ujRkmmbF946w==", "path": "microsoft.extensions.localization/9.0.1", "hashPath": "microsoft.extensions.localization.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CABog43lyaZQMjmlktuImCy6zmAzRBaXqN81uPaMQjlp//ISDVYItZPh6KWpWRF4MY/B67X5oDc3JTUpfdocZw==", "path": "microsoft.extensions.localization.abstractions/9.0.1", "hashPath": "microsoft.extensions.localization.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-w2gUqXN/jNIuvqYwX3lbXagsizVNXYyt6LlF57+tMve4JYCEgCMMAjRce6uKcDASJgpMbErRT1PfHy2OhbkqEA==", "path": "microsoft.extensions.logging.abstractions/9.0.1", "hashPath": "microsoft.extensions.logging.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nggoNKnWcsBIAaOWHA+53XZWrslC7aGeok+aR+epDPRy7HI7GwMnGZE8yEsL2Onw7kMOHVHwKcsDls1INkNUJQ==", "path": "microsoft.extensions.options/9.0.1", "hashPath": "microsoft.extensions.options.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-bHtTesA4lrSGD1ZUaMIx6frU3wyy0vYtTa/hM6gGQu5QNrydObv8T5COiGUWsisflAfmsaFOe9Xvw5NSO99z0g==", "path": "microsoft.extensions.primitives/9.0.1", "hashPath": "microsoft.extensions.primitives.9.0.1.nupkg.sha512"}, "Microsoft.JSInterop/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/xBwIfb0YoC2Muv6EsHjxpqZw2aKv94+i0g0FWZvqvGv3DeAy+8wipAuECVvKYEs2EIclRD41bjajHLoD6mTtw==", "path": "microsoft.jsinterop/9.0.1", "hashPath": "microsoft.jsinterop.9.0.1.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-rd1CbIsMtVPtZNTIVD6Xydue//klYOOQIDpRgu3BHtv17AlpRs74/6QFbcYgMm/jL+naVU2T3OFLxVSLV5lQLQ==", "path": "microsoft.net.illink.tasks/9.0.8", "hashPath": "microsoft.net.illink.tasks.9.0.8.nupkg.sha512"}, "MudBlazor.JSCompiler/1.0.17": {"type": "package", "serviceable": true, "sha512": "sha512-cWsV0rETNdk6yGkqonfJvuAqgyKU01b8kL2FJg+Xq0v9ZejgsuP2L/vMR/RTZuuhkCQRHPhKFLmpK9YG+vtAWg==", "path": "mudblazor.jscompiler/1.0.17", "hashPath": "mudblazor.jscompiler.1.0.17.nupkg.sha512"}, "MudBlazor.SassCompiler/2.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-l9qO3CQjPpX59ctYEUKLThu/hL4NxvtOaANm+jp0DKbczhOuCZsomD2Vul1UUTSr5km7uNKPQxTej57WqbsqHg==", "path": "mudblazor.sasscompiler/2.0.7", "hashPath": "mudblazor.sasscompiler.2.0.7.nupkg.sha512"}}}