﻿<Project>
  <ItemGroup>
    <StaticWebAssetEndpoint Include="_content/MudBlazor/MudBlazor.min.bj7ppmloxe.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"bj7ppmloxe"},{"Name":"integrity","Value":"sha256-ZxMYXdGvG68LxMTAoTTaLf\u002B\u002BnTS/uJ9kk\u002B0w4nTG24c="},{"Name":"label","Value":"_content/MudBlazor/MudBlazor.min.js"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"55250"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ZxMYXdGvG68LxMTAoTTaLf\u002B\u002BnTS/uJ9kk\u002B0w4nTG24c=\u0022"},{"Name":"Last-Modified","Value":"Tue, 09 Sep 2025 13:57:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MudBlazor/MudBlazor.min.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"595896"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 09 Sep 2025 13:57:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MudBlazor/MudBlazor.min.hmu6kx9mgf.css">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.css'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"fingerprint","Value":"hmu6kx9mgf"},{"Name":"integrity","Value":"sha256-d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU="},{"Name":"label","Value":"_content/MudBlazor/MudBlazor.min.css"}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"max-age=31536000, immutable"},{"Name":"Content-Length","Value":"595896"},{"Name":"Content-Type","Value":"text/css"},{"Name":"ETag","Value":"\u0022d1ee8m8CKXHLkW0DDwn00o/ghDNhscMVl/ei7IzThfU=\u0022"},{"Name":"Last-Modified","Value":"Tue, 09 Sep 2025 13:57:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
    <StaticWebAssetEndpoint Include="_content/MudBlazor/MudBlazor.min.js">
      <AssetFile>$([System.IO.Path]::GetFullPath('$(MSBuildThisFileDirectory)..\staticwebassets\MudBlazor.min.js'))</AssetFile>
      <Selectors><![CDATA[[]]]></Selectors>
      <EndpointProperties><![CDATA[[{"Name":"integrity","Value":"sha256-ZxMYXdGvG68LxMTAoTTaLf\u002B\u002BnTS/uJ9kk\u002B0w4nTG24c="}]]]></EndpointProperties>
      <ResponseHeaders><![CDATA[[{"Name":"Accept-Ranges","Value":"bytes"},{"Name":"Cache-Control","Value":"no-cache"},{"Name":"Content-Length","Value":"55250"},{"Name":"Content-Type","Value":"text/javascript"},{"Name":"ETag","Value":"\u0022ZxMYXdGvG68LxMTAoTTaLf\u002B\u002BnTS/uJ9kk\u002B0w4nTG24c=\u0022"},{"Name":"Last-Modified","Value":"Tue, 09 Sep 2025 13:57:15 GMT"}]]]></ResponseHeaders>
    </StaticWebAssetEndpoint>
  </ItemGroup>
</Project>