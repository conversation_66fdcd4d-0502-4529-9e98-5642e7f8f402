﻿@namespace MudBlazor
@using MudBlazor.Charts
@inherits MudTimeSeriesChartBase

<CascadingValue Value="@this" IsFixed="true">
    <CascadingValue Value="@((MudChartBase)this)" IsFixed="true">
        <div @attributes="UserAttributes" class="@Classname" style="@Style" dir="ltr">
            <TimeSeries XAxisTitle="@XAxisTitle" YAxisTitle="@YAxisTitle" ChartSeries="@ChartSeries" @bind-SelectedIndex="@SelectedIndex" TimeLabelSpacing="@TimeLabelSpacing" TimeLabelFormat="@TimeLabelFormat"></TimeSeries>
        </div>
    </CascadingValue>
</CascadingValue>
