{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "ignorePresets": [":ignoreModulesAndTests", "mergeConfidence:all-badges"], "labels": ["dependency"], "packageRules": [{"matchPackageNames": ["Microsoft.CodeAnalysis**"], "groupName": "code-analysis monorepo"}, {"matchPackageNames": ["Microsoft.CodeAnalysis.CSharp"], "enabled": false}, {"matchPackageNames": ["FluentAssertions"], "allowedVersions": "< 8.0.0"}]}